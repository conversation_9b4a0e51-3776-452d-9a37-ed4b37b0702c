package com.videoplayer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 应用配置类
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@Configuration
@ConfigurationProperties(prefix = "app")
@Validated
public class AppConfig {

    /**
     * 应用基本信息
     */
    @NotBlank(message = "应用名称不能为空")
    private String name = "视频播放器";

    @NotBlank(message = "应用版本不能为空")
    private String version = "2.0.0";

    private String description = "专业的网页视频播放器";

    /**
     * 文件上传配置
     */
    private Upload upload = new Upload();

    /**
     * 安全配置
     */
    private Security security = new Security();

    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    /**
     * 阿里云OSS配置
     */
    private Oss oss = new Oss();

    // 文件上传配置
    public static class Upload {
        @NotNull(message = "最大文件大小不能为空")
        @Min(value = 1, message = "最大文件大小必须大于0")
        @Max(value = 10737418240L, message = "最大文件大小不能超过10GB")
        private Long maxFileSize = 104857600L; // 100MB

        @NotNull(message = "允许的文件类型不能为空")
        private String[] allowedTypes = {"mp4", "avi", "mov", "wmv", "flv", "webm", "mkv"};

        @NotBlank(message = "上传路径不能为空")
        private String path = "/uploads";

        // Getters and Setters
        public Long getMaxFileSize() {
            return maxFileSize;
        }

        public void setMaxFileSize(Long maxFileSize) {
            this.maxFileSize = maxFileSize;
        }

        public String[] getAllowedTypes() {
            return allowedTypes;
        }

        public void setAllowedTypes(String[] allowedTypes) {
            this.allowedTypes = allowedTypes;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }
    }

    // 安全配置
    public static class Security {
        private boolean enableXssProtection = true;
        private boolean enableSqlInjectionProtection = true;
        private boolean enableCsrfProtection = false; // API应用通常不需要CSRF保护
        private boolean enableRateLimiting = true;

        @Min(value = 1, message = "最大请求次数必须大于0")
        private int maxRequestsPerMinute = 100;

        @Min(value = 1, message = "会话超时时间必须大于0")
        private int sessionTimeoutMinutes = 30;

        private String[] trustedDomains = {"localhost", "127.0.0.1"};

        // Getters and Setters
        public boolean isEnableXssProtection() {
            return enableXssProtection;
        }

        public void setEnableXssProtection(boolean enableXssProtection) {
            this.enableXssProtection = enableXssProtection;
        }

        public boolean isEnableSqlInjectionProtection() {
            return enableSqlInjectionProtection;
        }

        public void setEnableSqlInjectionProtection(boolean enableSqlInjectionProtection) {
            this.enableSqlInjectionProtection = enableSqlInjectionProtection;
        }

        public boolean isEnableCsrfProtection() {
            return enableCsrfProtection;
        }

        public void setEnableCsrfProtection(boolean enableCsrfProtection) {
            this.enableCsrfProtection = enableCsrfProtection;
        }

        public boolean isEnableRateLimiting() {
            return enableRateLimiting;
        }

        public void setEnableRateLimiting(boolean enableRateLimiting) {
            this.enableRateLimiting = enableRateLimiting;
        }

        public int getMaxRequestsPerMinute() {
            return maxRequestsPerMinute;
        }

        public void setMaxRequestsPerMinute(int maxRequestsPerMinute) {
            this.maxRequestsPerMinute = maxRequestsPerMinute;
        }

        public int getSessionTimeoutMinutes() {
            return sessionTimeoutMinutes;
        }

        public void setSessionTimeoutMinutes(int sessionTimeoutMinutes) {
            this.sessionTimeoutMinutes = sessionTimeoutMinutes;
        }

        public String[] getTrustedDomains() {
            return trustedDomains;
        }

        public void setTrustedDomains(String[] trustedDomains) {
            this.trustedDomains = trustedDomains;
        }
    }

    // 缓存配置
    public static class Cache {
        private boolean enabled = true;

        @Min(value = 1, message = "缓存过期时间必须大于0")
        private int ttlMinutes = 60;

        @Min(value = 1, message = "最大缓存大小必须大于0")
        private int maxSize = 1000;

        private String type = "memory"; // memory, redis

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getTtlMinutes() {
            return ttlMinutes;
        }

        public void setTtlMinutes(int ttlMinutes) {
            this.ttlMinutes = ttlMinutes;
        }

        public int getMaxSize() {
            return maxSize;
        }

        public void setMaxSize(int maxSize) {
            this.maxSize = maxSize;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }

    // 阿里云OSS配置
    public static class Oss {
        private String endpoint = "https://oss-cn-guangzhou.aliyuncs.com";
        private String bucket = "jyqk";
        private String dir = "video-player/videos";
        private String baseUrl = "https://jyqk.oss-cn-guangzhou.aliyuncs.com";
        private String accessKeyId;
        private String accessKeySecret;

        // Getters and Setters
        public String getEndpoint() {
            return endpoint;
        }

        public void setEndpoint(String endpoint) {
            this.endpoint = endpoint;
        }

        public String getBucket() {
            return bucket;
        }

        public void setBucket(String bucket) {
            this.bucket = bucket;
        }

        public String getDir() {
            return dir;
        }

        public void setDir(String dir) {
            this.dir = dir;
        }

        public String getBaseUrl() {
            return baseUrl;
        }

        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }

        public String getAccessKeyId() {
            return accessKeyId;
        }

        public void setAccessKeyId(String accessKeyId) {
            this.accessKeyId = accessKeyId;
        }

        public String getAccessKeySecret() {
            return accessKeySecret;
        }

        public void setAccessKeySecret(String accessKeySecret) {
            this.accessKeySecret = accessKeySecret;
        }
    }

    // 主类的Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Upload getUpload() {
        return upload;
    }

    public void setUpload(Upload upload) {
        this.upload = upload;
    }

    public Security getSecurity() {
        return security;
    }

    public void setSecurity(Security security) {
        this.security = security;
    }

    public Cache getCache() {
        return cache;
    }

    public void setCache(Cache cache) {
        this.cache = cache;
    }

    public Oss getOss() {
        return oss;
    }

    public void setOss(Oss oss) {
        this.oss = oss;
    }
}
