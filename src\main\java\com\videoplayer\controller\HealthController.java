package com.videoplayer.controller;

import com.videoplayer.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@RestController
@RequestMapping("/health")
public class HealthController implements HealthIndicator {

    @Autowired
    private DataSource dataSource;

    /**
     * 简单健康检查
     */
    @GetMapping
    public ResponseEntity<String> simpleHealth() {
        return ResponseEntity.ok("OK");
    }

    /**
     * 详细健康检查
     */
    @GetMapping("/detailed")
    public ResponseEntity<ApiResponse<Map<String, Object>>> detailedHealth() {
        Map<String, Object> healthInfo = new HashMap<>();
        
        try {
            // 检查应用状态
            healthInfo.put("application", checkApplicationHealth());
            
            // 检查数据库连接
            healthInfo.put("database", checkDatabaseHealth());
            
            // 检查内存使用
            healthInfo.put("memory", checkMemoryHealth());
            
            // 检查磁盘空间
            healthInfo.put("disk", checkDiskHealth());
            
            // 总体状态
            boolean allHealthy = healthInfo.values().stream()
                .allMatch(status -> "UP".equals(((Map<?, ?>) status).get("status")));
            
            healthInfo.put("overall", allHealthy ? "UP" : "DOWN");
            
            return ResponseEntity.ok(ApiResponse.success("健康检查完成", healthInfo));
            
        } catch (Exception e) {
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("status", "DOWN");
            errorInfo.put("error", e.getMessage());
            healthInfo.put("overall", "DOWN");
            healthInfo.put("error", errorInfo);
            
            return ResponseEntity.status(503)
                .body(ApiResponse.error("HEALTH_CHECK_FAILED", "健康检查失败"));
        }
    }

    /**
     * Spring Boot Actuator健康检查
     */
    @Override
    public Health health() {
        try {
            // 检查数据库连接
            if (isDatabaseHealthy()) {
                return Health.up()
                    .withDetail("database", "UP")
                    .withDetail("timestamp", System.currentTimeMillis())
                    .build();
            } else {
                return Health.down()
                    .withDetail("database", "DOWN")
                    .withDetail("timestamp", System.currentTimeMillis())
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .withDetail("timestamp", System.currentTimeMillis())
                .build();
        }
    }

    /**
     * 检查应用健康状态
     */
    private Map<String, Object> checkApplicationHealth() {
        Map<String, Object> appHealth = new HashMap<>();
        
        try {
            // 检查JVM运行时间
            long uptime = java.lang.management.ManagementFactory.getRuntimeMXBean().getUptime();
            
            appHealth.put("status", "UP");
            appHealth.put("uptime", uptime);
            appHealth.put("uptimeFormatted", formatUptime(uptime));
            appHealth.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            appHealth.put("status", "DOWN");
            appHealth.put("error", e.getMessage());
        }
        
        return appHealth;
    }

    /**
     * 检查数据库健康状态
     */
    private Map<String, Object> checkDatabaseHealth() {
        Map<String, Object> dbHealth = new HashMap<>();
        
        try {
            if (isDatabaseHealthy()) {
                dbHealth.put("status", "UP");
                dbHealth.put("database", "MySQL");
            } else {
                dbHealth.put("status", "DOWN");
                dbHealth.put("error", "无法连接到数据库");
            }
        } catch (Exception e) {
            dbHealth.put("status", "DOWN");
            dbHealth.put("error", e.getMessage());
        }
        
        return dbHealth;
    }

    /**
     * 检查内存健康状态
     */
    private Map<String, Object> checkMemoryHealth() {
        Map<String, Object> memoryHealth = new HashMap<>();
        
        try {
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            double usagePercent = (double) usedMemory / maxMemory * 100;
            
            memoryHealth.put("status", usagePercent < 90 ? "UP" : "WARNING");
            memoryHealth.put("used", usedMemory);
            memoryHealth.put("free", freeMemory);
            memoryHealth.put("total", totalMemory);
            memoryHealth.put("max", maxMemory);
            memoryHealth.put("usagePercent", Math.round(usagePercent * 100.0) / 100.0);
            
        } catch (Exception e) {
            memoryHealth.put("status", "DOWN");
            memoryHealth.put("error", e.getMessage());
        }
        
        return memoryHealth;
    }

    /**
     * 检查磁盘健康状态
     */
    private Map<String, Object> checkDiskHealth() {
        Map<String, Object> diskHealth = new HashMap<>();
        
        try {
            java.io.File root = new java.io.File("/");
            long totalSpace = root.getTotalSpace();
            long freeSpace = root.getFreeSpace();
            long usedSpace = totalSpace - freeSpace;
            
            double usagePercent = (double) usedSpace / totalSpace * 100;
            
            diskHealth.put("status", usagePercent < 90 ? "UP" : "WARNING");
            diskHealth.put("total", totalSpace);
            diskHealth.put("free", freeSpace);
            diskHealth.put("used", usedSpace);
            diskHealth.put("usagePercent", Math.round(usagePercent * 100.0) / 100.0);
            
        } catch (Exception e) {
            diskHealth.put("status", "DOWN");
            diskHealth.put("error", e.getMessage());
        }
        
        return diskHealth;
    }

    /**
     * 检查数据库是否健康
     */
    private boolean isDatabaseHealthy() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5); // 5秒超时
        } catch (SQLException e) {
            return false;
        }
    }

    /**
     * 格式化运行时间
     */
    private String formatUptime(long uptimeMs) {
        long seconds = uptimeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%d天 %d小时 %d分钟", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%d小时 %d分钟", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟 %d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
}
