package com.videoplayer.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * 缓存配置类
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@Configuration
@EnableCaching
public class CacheConfig {

    // 缓存名称常量
    public static final String VIDEO_CACHE = "videos";
    public static final String VIDEO_LIST_CACHE = "videoList";
    public static final String CONTACT_CACHE = "contacts";
    public static final String CATEGORY_CACHE = "categories";
    public static final String POPULAR_VIDEOS_CACHE = "popularVideos";
    public static final String LATEST_VIDEOS_CACHE = "latestVideos";

    /**
     * 默认缓存管理器
     */
    @Bean
    @Primary
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        cacheManager.setCacheNames(java.util.Arrays.asList(
            VIDEO_CACHE,
            VIDEO_LIST_CACHE,
            CONTACT_CACHE,
            CATEGORY_CACHE,
            POPULAR_VIDEOS_CACHE,
            LATEST_VIDEOS_CACHE
        ));
        return cacheManager;
    }

    /**
     * 自定义缓存管理器（支持TTL）
     */
    @Bean("customCacheManager")
    public CacheManager customCacheManager() {
        return new CustomCacheManager();
    }

    /**
     * 自定义缓存管理器实现
     */
    public static class CustomCacheManager extends ConcurrentMapCacheManager {
        
        public CustomCacheManager() {
            super();
        }

        @Override
        protected org.springframework.cache.Cache createConcurrentMapCache(String name) {
            return new CustomCache(name);
        }
    }

    /**
     * 自定义缓存实现（支持TTL）
     */
    public static class CustomCache implements org.springframework.cache.Cache {
        
        private final String name;
        private final java.util.concurrent.ConcurrentHashMap<Object, CacheValue> cache;
        private final long defaultTtl;

        public CustomCache(String name) {
            this.name = name;
            this.cache = new java.util.concurrent.ConcurrentHashMap<>();
            this.defaultTtl = TimeUnit.MINUTES.toMillis(60); // 默认1小时
            
            // 启动清理线程
            startCleanupTask();
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public Object getNativeCache() {
            return cache;
        }

        @Override
        public ValueWrapper get(Object key) {
            CacheValue cacheValue = cache.get(key);
            if (cacheValue != null && !cacheValue.isExpired()) {
                return () -> cacheValue.getValue();
            } else if (cacheValue != null) {
                cache.remove(key); // 移除过期的缓存
            }
            return null;
        }

        @Override
        public <T> T get(Object key, Class<T> type) {
            ValueWrapper wrapper = get(key);
            if (wrapper != null) {
                Object value = wrapper.get();
                if (type.isInstance(value)) {
                    return type.cast(value);
                }
            }
            return null;
        }

        @Override
        public <T> T get(Object key, java.util.concurrent.Callable<T> valueLoader) {
            ValueWrapper wrapper = get(key);
            if (wrapper != null) {
                return (T) wrapper.get();
            }
            
            try {
                T value = valueLoader.call();
                put(key, value);
                return value;
            } catch (Exception e) {
                throw new RuntimeException("缓存值加载失败", e);
            }
        }

        @Override
        public void put(Object key, Object value) {
            cache.put(key, new CacheValue(value, System.currentTimeMillis() + defaultTtl));
        }

        @Override
        public void evict(Object key) {
            cache.remove(key);
        }

        @Override
        public void clear() {
            cache.clear();
        }

        /**
         * 启动清理任务
         */
        private void startCleanupTask() {
            java.util.concurrent.Executors.newSingleThreadScheduledExecutor(r -> {
                Thread t = new Thread(r, "Cache-Cleanup-" + name);
                t.setDaemon(true);
                return t;
            }).scheduleAtFixedRate(this::cleanup, 5, 5, TimeUnit.MINUTES);
        }

        /**
         * 清理过期缓存
         */
        private void cleanup() {
            cache.entrySet().removeIf(entry -> entry.getValue().isExpired());
        }

        /**
         * 缓存值包装类
         */
        private static class CacheValue {
            private final Object value;
            private final long expireTime;

            public CacheValue(Object value, long expireTime) {
                this.value = value;
                this.expireTime = expireTime;
            }

            public Object getValue() {
                return value;
            }

            public boolean isExpired() {
                return System.currentTimeMillis() > expireTime;
            }
        }
    }
}
