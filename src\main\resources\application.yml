server:
  port: 5000
  servlet:
    context-path: /

spring:
  application:
    name: video-player
  
  # 数据库配置
  datasource:
    url: ******************************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: false
    
  # Thymeleaf配置
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    cache: false
    encoding: UTF-8
    mode: HTML
    
  # 阿里云OSS配置
  cloud:
    alicloud:
      oss:
        endpoint: ${OSS_ENDPOINT:https://oss-cn-guangzhou.aliyuncs.com}
        bucket: ${OSS_BUCKET:jyqk}
        dir: ${OSS_DIR:video-player/videos}
        baseUrl: ${OSS_BASE_URL:https://jyqk.oss-cn-guangzhou.aliyuncs.com}
    
  # 静态资源配置
  web:
    resources:
      static-locations: classpath:/static/
      cache:
        period: 0
        
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB

  # 视频流优化配置
  mvc:
    async:
      request-timeout: 30000 # 30秒超时

  # HTTP编码配置
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true


# 日志配置
logging:
  level:
    com.videoplayer: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 自定义配置
app:
  name: 视频播放器
  version: 2.0.0
  description: 专业的网页视频播放器

  # 文件上传配置
  upload:
    max-file-size: 104857600 # 100MB
    allowed-types:
      - mp4
      - avi
      - mov
      - wmv
      - flv
      - webm
      - mkv
    path: uploads

  # 安全配置
  security:
    enable-xss-protection: true
    enable-sql-injection-protection: true
    enable-csrf-protection: false
    enable-rate-limiting: true
    max-requests-per-minute: 100
    session-timeout-minutes: 30
    trusted-domains:
      - localhost
      - 127.0.0.1

  # 缓存配置
  cache:
    enabled: true
    ttl-minutes: 60
    max-size: 1000
    type: redis

  # 阿里云OSS配置
  oss:
    endpoint: https://oss-cn-guangzhou.aliyuncs.com
    bucket: jyqk
    dir: video-player/videos
    base-url: https://jyqk.oss-cn-guangzhou.aliyuncs.com

  # 视频播放优化配置
  video:
    # 流媒体配置
    streaming:
      enabled: true
      buffer-size: 8192 # 8KB缓冲区
      chunk-size: 1048576 # 1MB分片大小
      max-concurrent-streams: 10
      enable-range-requests: true

    # 缓存配置
    cache:
      enabled: true
      max-size: 100 # 最大缓存视频数
      ttl-hours: 24 # 缓存24小时
      preload-segments: 3 # 预加载片段数

    # 质量控制
    quality:
      adaptive-enabled: true
      default-quality: auto
      quality-levels: [240p, 360p, 480p, 720p, 1080p]
      bandwidth-thresholds:
        240p: 200
        360p: 400
        480p: 800
        720p: 1500
        1080p: 3000

    # 性能监控
    monitoring:
      enabled: true
      collect-stats: true
      stats-interval: 5000 # 5秒收集一次统计
      log-performance: true

# 兼容旧配置
video:
  oss:
    endpoint: ${OSS_ENDPOINT:}
    access-key-id: ${OSS_ACCESS_KEY_ID:}
    access-key-secret: ${OSS_ACCESS_KEY_SECRET:}
    bucket-name: ${OSS_BUCKET_NAME:}
