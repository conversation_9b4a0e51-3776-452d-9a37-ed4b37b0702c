package com.videoplayer.controller;

import com.videoplayer.aspect.PerformanceAspect;
import com.videoplayer.common.ApiResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;
import java.util.HashMap;
import java.util.Map;

/**
 * 性能指标控制器
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@RestController
@RequestMapping("/api/metrics")
public class MetricsController {

    /**
     * 获取应用性能指标
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Map<String, Object>>> getMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        // 应用指标
        metrics.put("application", getApplicationMetrics());
        
        // 系统指标
        metrics.put("system", getSystemMetrics());
        
        // 内存指标
        metrics.put("memory", getMemoryMetrics());
        
        // 自定义性能指标
        metrics.put("performance", PerformanceAspect.MetricsCollector.getAllMetrics());
        
        return ResponseEntity.ok(ApiResponse.success("获取性能指标成功", metrics));
    }

    /**
     * 获取应用指标
     */
    private Map<String, Object> getApplicationMetrics() {
        Map<String, Object> appMetrics = new HashMap<>();
        
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        
        appMetrics.put("uptime", runtimeBean.getUptime());
        appMetrics.put("startTime", runtimeBean.getStartTime());
        appMetrics.put("vmName", runtimeBean.getVmName());
        appMetrics.put("vmVersion", runtimeBean.getVmVersion());
        appMetrics.put("vmVendor", runtimeBean.getVmVendor());
        
        return appMetrics;
    }

    /**
     * 获取系统指标
     */
    private Map<String, Object> getSystemMetrics() {
        Map<String, Object> systemMetrics = new HashMap<>();
        
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        
        systemMetrics.put("osName", osBean.getName());
        systemMetrics.put("osVersion", osBean.getVersion());
        systemMetrics.put("osArch", osBean.getArch());
        systemMetrics.put("availableProcessors", osBean.getAvailableProcessors());
        systemMetrics.put("systemLoadAverage", osBean.getSystemLoadAverage());
        
        return systemMetrics;
    }

    /**
     * 获取内存指标
     */
    private Map<String, Object> getMemoryMetrics() {
        Map<String, Object> memoryMetrics = new HashMap<>();
        
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        Runtime runtime = Runtime.getRuntime();
        
        // JVM内存
        memoryMetrics.put("heapMemoryUsed", memoryBean.getHeapMemoryUsage().getUsed());
        memoryMetrics.put("heapMemoryMax", memoryBean.getHeapMemoryUsage().getMax());
        memoryMetrics.put("heapMemoryCommitted", memoryBean.getHeapMemoryUsage().getCommitted());
        
        memoryMetrics.put("nonHeapMemoryUsed", memoryBean.getNonHeapMemoryUsage().getUsed());
        memoryMetrics.put("nonHeapMemoryMax", memoryBean.getNonHeapMemoryUsage().getMax());
        memoryMetrics.put("nonHeapMemoryCommitted", memoryBean.getNonHeapMemoryUsage().getCommitted());
        
        // Runtime内存
        memoryMetrics.put("totalMemory", runtime.totalMemory());
        memoryMetrics.put("freeMemory", runtime.freeMemory());
        memoryMetrics.put("maxMemory", runtime.maxMemory());
        memoryMetrics.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
        
        // 内存使用率
        double memoryUsagePercent = ((double) (runtime.totalMemory() - runtime.freeMemory()) / runtime.maxMemory()) * 100;
        memoryMetrics.put("memoryUsagePercent", Math.round(memoryUsagePercent * 100.0) / 100.0);
        
        return memoryMetrics;
    }

    /**
     * 获取缓存统计信息
     */
    @GetMapping("/cache")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCacheMetrics() {
        Map<String, Object> cacheMetrics = new HashMap<>();
        
        // 这里可以添加缓存统计信息
        // 例如：缓存命中率、缓存大小、缓存清理次数等
        
        cacheMetrics.put("message", "缓存指标功能待实现");
        
        return ResponseEntity.ok(ApiResponse.success("获取缓存指标成功", cacheMetrics));
    }

    /**
     * 获取数据库连接池指标
     */
    @GetMapping("/database")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getDatabaseMetrics() {
        Map<String, Object> dbMetrics = new HashMap<>();
        
        // 这里可以添加数据库连接池统计信息
        // 例如：活跃连接数、空闲连接数、等待连接数等
        
        dbMetrics.put("message", "数据库指标功能待实现");
        
        return ResponseEntity.ok(ApiResponse.success("获取数据库指标成功", dbMetrics));
    }

    /**
     * 清除性能指标
     */
    @GetMapping("/clear")
    public ResponseEntity<ApiResponse<Void>> clearMetrics() {
        PerformanceAspect.MetricsCollector.clear();
        return ResponseEntity.ok(ApiResponse.successVoid("性能指标已清除"));
    }

    /**
     * 获取健康检查信息
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getHealthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 检查内存使用率
            Runtime runtime = Runtime.getRuntime();
            double memoryUsage = ((double) (runtime.totalMemory() - runtime.freeMemory()) / runtime.maxMemory()) * 100;
            
            // 检查系统负载
            OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
            double systemLoad = osBean.getSystemLoadAverage();
            
            // 健康状态判断
            String status = "UP";
            if (memoryUsage > 90) {
                status = "DOWN";
            } else if (memoryUsage > 80 || systemLoad > 0.8) {
                status = "WARNING";
            }
            
            health.put("status", status);
            health.put("memoryUsage", Math.round(memoryUsage * 100.0) / 100.0);
            health.put("systemLoad", systemLoad);
            health.put("timestamp", System.currentTimeMillis());
            
            // 组件健康检查
            Map<String, String> components = new HashMap<>();
            components.put("database", "UP"); // 这里可以实际检查数据库连接
            components.put("cache", "UP");    // 这里可以实际检查缓存状态
            components.put("storage", "UP");  // 这里可以实际检查存储状态
            
            health.put("components", components);
            
        } catch (Exception e) {
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
        }
        
        return ResponseEntity.ok(ApiResponse.success("健康检查完成", health));
    }

    /**
     * 触发垃圾回收
     */
    @GetMapping("/gc")
    public ResponseEntity<ApiResponse<Map<String, Object>>> triggerGarbageCollection() {
        Map<String, Object> result = new HashMap<>();
        
        // 记录GC前的内存状态
        Runtime runtime = Runtime.getRuntime();
        long beforeGC = runtime.totalMemory() - runtime.freeMemory();
        
        // 触发GC
        System.gc();
        
        // 等待一小段时间让GC完成
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 记录GC后的内存状态
        long afterGC = runtime.totalMemory() - runtime.freeMemory();
        long freedMemory = beforeGC - afterGC;
        
        result.put("memoryBeforeGC", beforeGC);
        result.put("memoryAfterGC", afterGC);
        result.put("freedMemory", freedMemory);
        result.put("freedMemoryMB", freedMemory / (1024 * 1024));
        
        return ResponseEntity.ok(ApiResponse.success("垃圾回收已触发", result));
    }
}
