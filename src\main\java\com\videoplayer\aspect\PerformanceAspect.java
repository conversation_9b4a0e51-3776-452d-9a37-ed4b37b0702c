package com.videoplayer.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 性能监控切面
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@Aspect
@Component
public class PerformanceAspect {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceAspect.class);
    
    // 性能阈值（毫秒）
    private static final long SLOW_QUERY_THRESHOLD = 1000;
    private static final long WARNING_THRESHOLD = 500;

    /**
     * 监控Service层方法性能
     */
    @Around("execution(* com.videoplayer.service.*.*(..))")
    public Object monitorServicePerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethodPerformance(joinPoint, "SERVICE");
    }

    /**
     * 监控Controller层方法性能
     */
    @Around("execution(* com.videoplayer.controller.*.*(..))")
    public Object monitorControllerPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethodPerformance(joinPoint, "CONTROLLER");
    }

    /**
     * 监控Repository层方法性能
     */
    @Around("execution(* com.videoplayer.repository.*.*(..))")
    public Object monitorRepositoryPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethodPerformance(joinPoint, "REPOSITORY");
    }

    /**
     * 通用性能监控方法
     */
    private Object monitorMethodPerformance(ProceedingJoinPoint joinPoint, String layer) throws Throwable {
        long startTime = System.currentTimeMillis();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        try {
            // 执行目标方法
            Object result = joinPoint.proceed();
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 记录性能日志
            logPerformance(layer, className, methodName, args, executionTime, null);
            
            return result;
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 记录异常和性能日志
            logPerformance(layer, className, methodName, args, executionTime, e);
            
            throw e;
        }
    }

    /**
     * 记录性能日志
     */
    private void logPerformance(String layer, String className, String methodName, 
                               Object[] args, long executionTime, Exception exception) {
        
        String methodSignature = String.format("%s.%s.%s", layer, className, methodName);
        
        if (exception != null) {
            // 异常情况
            logger.error("方法执行异常 - {} 耗时: {}ms, 参数: {}, 异常: {}", 
                        methodSignature, executionTime, formatArgs(args), exception.getMessage());
        } else if (executionTime > SLOW_QUERY_THRESHOLD) {
            // 慢查询
            logger.warn("慢查询检测 - {} 耗时: {}ms, 参数: {}", 
                       methodSignature, executionTime, formatArgs(args));
        } else if (executionTime > WARNING_THRESHOLD) {
            // 性能警告
            logger.info("性能警告 - {} 耗时: {}ms, 参数: {}", 
                       methodSignature, executionTime, formatArgs(args));
        } else {
            // 正常情况（调试级别）
            logger.debug("方法执行 - {} 耗时: {}ms", methodSignature, executionTime);
        }
        
        // 记录性能指标到监控系统（如果有的话）
        recordMetrics(layer, className, methodName, executionTime, exception != null);
    }

    /**
     * 格式化参数
     */
    private String formatArgs(Object[] args) {
        if (args == null || args.length == 0) {
            return "[]";
        }
        
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < args.length; i++) {
            if (i > 0) {
                sb.append(", ");
            }
            
            Object arg = args[i];
            if (arg == null) {
                sb.append("null");
            } else if (arg instanceof String) {
                // 限制字符串长度
                String str = (String) arg;
                if (str.length() > 100) {
                    sb.append("\"").append(str.substring(0, 100)).append("...\"");
                } else {
                    sb.append("\"").append(str).append("\"");
                }
            } else if (arg instanceof Number) {
                sb.append(arg);
            } else {
                // 其他类型显示类名
                sb.append(arg.getClass().getSimpleName());
            }
        }
        sb.append("]");
        
        return sb.toString();
    }

    /**
     * 记录性能指标
     */
    private void recordMetrics(String layer, String className, String methodName, 
                              long executionTime, boolean hasError) {
        // 这里可以集成监控系统，如Micrometer、Prometheus等
        // 示例：记录到内存中的指标收集器
        
        String metricName = String.format("%s.%s.%s", layer.toLowerCase(), className, methodName);
        
        // 记录执行时间
        MetricsCollector.recordExecutionTime(metricName, executionTime);
        
        // 记录调用次数
        MetricsCollector.incrementCounter(metricName + ".calls");
        
        // 记录错误次数
        if (hasError) {
            MetricsCollector.incrementCounter(metricName + ".errors");
        }
    }

    /**
     * 简单的指标收集器
     */
    public static class MetricsCollector {
        private static final java.util.concurrent.ConcurrentHashMap<String, Long> counters = 
            new java.util.concurrent.ConcurrentHashMap<>();
        private static final java.util.concurrent.ConcurrentHashMap<String, java.util.List<Long>> timings = 
            new java.util.concurrent.ConcurrentHashMap<>();

        public static void incrementCounter(String name) {
            counters.merge(name, 1L, Long::sum);
        }

        public static void recordExecutionTime(String name, long time) {
            timings.computeIfAbsent(name, k -> new java.util.concurrent.CopyOnWriteArrayList<>()).add(time);
        }

        public static Long getCounter(String name) {
            return counters.get(name);
        }

        public static java.util.List<Long> getTimings(String name) {
            return timings.get(name);
        }

        public static double getAverageTime(String name) {
            java.util.List<Long> times = timings.get(name);
            if (times == null || times.isEmpty()) {
                return 0.0;
            }
            return times.stream().mapToLong(Long::longValue).average().orElse(0.0);
        }

        public static java.util.Map<String, Object> getAllMetrics() {
            java.util.Map<String, Object> metrics = new java.util.HashMap<>();
            
            // 添加计数器
            counters.forEach((name, count) -> metrics.put(name, count));
            
            // 添加平均执行时间
            timings.forEach((name, times) -> {
                if (!times.isEmpty()) {
                    double avg = times.stream().mapToLong(Long::longValue).average().orElse(0.0);
                    metrics.put(name + ".avg_time", Math.round(avg * 100.0) / 100.0);
                }
            });
            
            return metrics;
        }

        public static void clear() {
            counters.clear();
            timings.clear();
        }
    }
}
