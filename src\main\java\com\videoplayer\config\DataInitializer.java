package com.videoplayer.config;

import com.videoplayer.entity.Video;
import com.videoplayer.entity.LeadContact;
import com.videoplayer.repository.VideoRepository;
import com.videoplayer.repository.LeadContactRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

/**
 * 数据初始化器 - 仅在开发模式下运行
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@Profile("dev")
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private VideoRepository videoRepository;
    
    @Autowired
    private LeadContactRepository leadContactRepository;

    @Override
    public void run(String... args) throws Exception {
        initializeVideos();
        initializeContacts();
    }

    private void initializeVideos() {
        if (videoRepository.count() == 0) {
            // 添加示例视频数据
            Video video1 = new Video();
            video1.setTitle("佳茵轻康：瘦身产品使用指南");
            video1.setDescription("这是一个关于佳茵轻康瘦身产品使用方法的详细指南视频，帮助您了解如何正确使用我们的产品来达到最佳效果。");
            video1.setVideoUrl("https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4");
            video1.setThumbnailUrl("https://via.placeholder.com/800x450/000000/FFFFFF?text=Video+1");
            video1.setDuration(120);
            video1.setVideoFormat("mp4");
            video1.setResolution("1080p");
            videoRepository.save(video1);

            Video video2 = new Video();
            video2.setTitle("健康生活方式分享");
            video2.setDescription("分享健康的生活方式和饮食习惯，帮助您建立更好的生活节奏。");
            video2.setVideoUrl("https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4");
            video2.setThumbnailUrl("https://via.placeholder.com/800x450/000000/FFFFFF?text=Video+2");
            video2.setDuration(180);
            video2.setVideoFormat("mp4");
            video2.setResolution("720p");
            videoRepository.save(video2);

            Video video3 = new Video();
            video3.setTitle("产品效果展示");
            video3.setDescription("真实用户使用产品后的效果展示和心得分享。");
            video3.setVideoUrl("https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4");
            video3.setThumbnailUrl("https://via.placeholder.com/800x450/000000/FFFFFF?text=Video+3");
            video3.setDuration(90);
            video3.setVideoFormat("mp4");
            video3.setResolution("1080p");
            videoRepository.save(video3);

            System.out.println("已初始化 " + videoRepository.count() + " 个示例视频");
        }
    }

    private void initializeContacts() {
        if (leadContactRepository.count() == 0) {
            // 添加联系信息数据
            LeadContact contact1 = new LeadContact();
            contact1.setContactName("林佳");
            contact1.setPhone("18722880704");
            contact1.setWechat("18722880704");
            contact1.setDouyin("黄林佳");
            contact1.setDouyinNickname("皮皮管理");
            contact1.setContactType("主管");
            contact1.setIsDefault(true);
            contact1.setSortOrder(1);
            leadContactRepository.save(contact1);

            LeadContact contact2 = new LeadContact();
            contact2.setContactName("黄超");
            contact2.setPhone("18057722960");
            contact2.setWechat("18057722960");
            contact2.setDouyin("黄超(黄小燕弟弟)");
            contact2.setDouyinNickname("黄超");
            contact2.setContactType("主管");
            contact2.setIsDefault(false);
            contact2.setSortOrder(2);
            leadContactRepository.save(contact2);

            LeadContact contact3 = new LeadContact();
            contact3.setContactName("小班");
            contact3.setPhone("15908542510");
            contact3.setWechat("15908542510");
            contact3.setDouyin("佳茵轻康SOS小班");
            contact3.setDouyinNickname("小班");
            contact3.setContactType("客服");
            contact3.setIsDefault(false);
            contact3.setSortOrder(3);
            leadContactRepository.save(contact3);

            System.out.println("已初始化 " + leadContactRepository.count() + " 个联系信息");
        }
    }
}
