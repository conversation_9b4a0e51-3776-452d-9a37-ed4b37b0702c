/**
 * 优化的视频播放器
 * Optimized Video Player for Better Performance
 * @version 3.0.0
 */

class OptimizedVideoPlayer {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.options = {
            autoplay: false,
            preload: 'metadata', // 优化预加载策略
            controls: true,
            fluid: true,
            responsive: true,
            playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 2],
            // 性能优化配置
            techOrder: ['html5'],
            html5: {
                vhs: {
                    overrideNative: true,
                    enableLowInitialPlaylist: true,
                    smoothQualityChange: true,
                    useBandwidthFromLocalStorage: true
                }
            },
            // 缓冲区优化
            liveui: false,
            liveTracker: {
                trackingThreshold: 20,
                liveTolerance: 15
            },
            ...options
        };
        
        this.player = null;
        this.performanceMetrics = {
            loadStartTime: null,
            firstFrameTime: null,
            bufferEvents: [],
            qualityChanges: [],
            errors: []
        };
        
        this.qualityController = new SimpleQualityController();
        
        this.init();
    }
    
    init() {
        if (!this.container) {
            console.error('视频容器未找到:', this.containerId);
            return;
        }
        
        this.setupPlayer();
        this.setupPerformanceMonitoring();
        this.setupAdaptiveStreaming();
        this.setupPreloading();
        this.bindEvents();
    }
    
    setupPlayer() {
        const videoElement = this.container.querySelector('video');
        if (!videoElement) {
            console.error('视频元素未找到');
            return;
        }
        
        // 设置视频元素属性优化
        videoElement.setAttribute('playsinline', '');
        videoElement.setAttribute('webkit-playsinline', '');
        videoElement.setAttribute('x5-video-player-type', 'h5');
        videoElement.setAttribute('x5-video-player-fullscreen', 'true');
        
        // 初始化Video.js播放器
        this.player = videojs(videoElement, this.options);
        
        // 添加插件
        this.setupPlugins();
        
        console.log('优化视频播放器初始化完成');
    }
    
    setupPlugins() {
        if (!this.player) return;
        
        // 添加质量选择器插件
        if (this.player.qualityLevels) {
            this.setupQualitySelector();
        }
        
        // 添加统计信息插件
        this.setupStatsPlugin();
        
        // 添加错误重试插件
        this.setupErrorRetry();
    }
    
    setupPerformanceMonitoring() {
        if (!this.player) return;
        
        // 监控加载性能
        this.player.on('loadstart', () => {
            this.performanceMetrics.loadStartTime = performance.now();
            console.log('开始加载视频');
        });
        
        this.player.on('loadedmetadata', () => {
            const loadTime = performance.now() - this.performanceMetrics.loadStartTime;
            console.log(`视频元数据加载完成，耗时: ${loadTime.toFixed(2)}ms`);
        });
        
        this.player.on('canplay', () => {
            const loadTime = performance.now() - this.performanceMetrics.loadStartTime;
            console.log(`视频可播放，总加载时间: ${loadTime.toFixed(2)}ms`);
        });
        
        // 监控缓冲事件
        this.player.on('waiting', () => {
            const bufferEvent = {
                time: this.player.currentTime(),
                timestamp: Date.now(),
                type: 'buffer_start'
            };
            this.performanceMetrics.bufferEvents.push(bufferEvent);
            console.log('视频缓冲开始', bufferEvent);
        });
        
        this.player.on('playing', () => {
            const bufferEvent = {
                time: this.player.currentTime(),
                timestamp: Date.now(),
                type: 'buffer_end'
            };
            this.performanceMetrics.bufferEvents.push(bufferEvent);
            console.log('视频缓冲结束', bufferEvent);
        });
        
        // 监控首帧渲染
        this.player.on('timeupdate', () => {
            if (!this.performanceMetrics.firstFrameTime && this.player.currentTime() > 0) {
                this.performanceMetrics.firstFrameTime = performance.now() - this.performanceMetrics.loadStartTime;
                console.log(`首帧渲染时间: ${this.performanceMetrics.firstFrameTime.toFixed(2)}ms`);
            }
        });
    }
    
    setupAdaptiveStreaming() {
        if (!this.player) return;

        // 监控播放质量
        this.player.on('qualitychange', (event) => {
            const qualityChange = {
                timestamp: Date.now(),
                from: event.oldQuality,
                to: event.newQuality,
                reason: event.reason || 'manual'
            };
            this.performanceMetrics.qualityChanges.push(qualityChange);
            console.log('视频质量变化:', qualityChange);
        });
    }
    
    setupPreloading() {
        // 智能预加载下一个视频片段
        if (this.player && this.options.preloadNext) {
            this.player.on('timeupdate', () => {
                const currentTime = this.player.currentTime();
                const duration = this.player.duration();
                
                // 当播放到80%时开始预加载下一个视频
                if (currentTime / duration > 0.8) {
                    this.preloadNextVideo();
                }
            });
        }
    }
    
    setupQualitySelector() {
        // 添加质量选择菜单
        const qualityLevels = this.player.qualityLevels();
        
        qualityLevels.on('addqualitylevel', (event) => {
            console.log('添加质量级别:', event.qualityLevel);
        });
        
        qualityLevels.on('change', (event) => {
            console.log('质量级别变化:', event);
        });
    }
    
    setupStatsPlugin() {
        // 添加播放统计信息
        this.player.ready(() => {
            this.addStatsOverlay();
        });
    }
    
    setupErrorRetry() {
        let retryCount = 0;
        const maxRetries = 3;
        
        this.player.on('error', (error) => {
            console.error('播放错误:', error);
            
            this.performanceMetrics.errors.push({
                timestamp: Date.now(),
                error: error,
                retryCount: retryCount
            });
            
            if (retryCount < maxRetries) {
                retryCount++;
                console.log(`尝试重新加载视频 (${retryCount}/${maxRetries})`);
                
                setTimeout(() => {
                    this.player.load();
                }, 1000 * retryCount); // 递增延迟重试
            } else {
                this.showErrorMessage('视频加载失败，请刷新页面重试');
            }
        });
    }
    
    bindEvents() {
        if (!this.player) return;
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (this.player && !e.target.matches('input, textarea')) {
                this.handleKeyboardShortcuts(e);
            }
        });
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && this.player && !this.player.paused()) {
                this.player.pause();
            }
        });
        
        // 网络状态变化
        window.addEventListener('online', () => {
            console.log('网络连接恢复');
            if (this.player && this.player.error()) {
                this.player.load();
            }
        });

        window.addEventListener('offline', () => {
            console.log('网络连接断开');
            this.showNetworkError();
        });
    }
    
    handleKeyboardShortcuts(e) {
        switch(e.code) {
            case 'Space':
                e.preventDefault();
                this.togglePlay();
                break;
            case 'ArrowLeft':
                e.preventDefault();
                this.seek(-10);
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.seek(10);
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.adjustVolume(0.1);
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.adjustVolume(-0.1);
                break;
            case 'KeyF':
                e.preventDefault();
                this.toggleFullscreen();
                break;
            case 'KeyM':
                e.preventDefault();
                this.toggleMute();
                break;
        }
    }
    
    // 播放控制方法
    togglePlay() {
        if (this.player.paused()) {
            this.player.play();
        } else {
            this.player.pause();
        }
    }
    
    seek(seconds) {
        const currentTime = this.player.currentTime();
        const newTime = Math.max(0, Math.min(currentTime + seconds, this.player.duration()));
        this.player.currentTime(newTime);
    }
    
    adjustVolume(delta) {
        const currentVolume = this.player.volume();
        const newVolume = Math.max(0, Math.min(1, currentVolume + delta));
        this.player.volume(newVolume);
    }
    
    toggleFullscreen() {
        if (this.player.isFullscreen()) {
            this.player.exitFullscreen();
        } else {
            this.player.requestFullscreen();
        }
    }
    
    toggleMute() {
        this.player.muted(!this.player.muted());
    }
    
    // 工具方法
    preloadNextVideo() {
        // 实现下一个视频的预加载逻辑
        console.log('预加载下一个视频');
    }
    
    addStatsOverlay() {
        // 添加性能统计覆盖层
        const statsButton = document.createElement('button');
        statsButton.className = 'vjs-stats-button vjs-control vjs-button';
        statsButton.innerHTML = '<span>统计</span>';
        statsButton.onclick = () => this.toggleStatsOverlay();
        
        const controlBar = this.player.controlBar.el();
        controlBar.appendChild(statsButton);
    }
    
    toggleStatsOverlay() {
        // 切换统计信息显示
        console.log('性能统计:', this.performanceMetrics);
    }
    
    showErrorMessage(message) {
        // 显示错误消息
        const errorDiv = document.createElement('div');
        errorDiv.className = 'video-error-message';
        errorDiv.textContent = message;
        this.container.appendChild(errorDiv);
    }
    
    showNetworkError() {
        this.showErrorMessage('网络连接已断开，请检查网络设置');
    }
    
    // 获取性能指标
    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            currentQuality: this.qualityController.getCurrentQuality()
        };
    }
    
    // 销毁播放器
    destroy() {
        if (this.player) {
            this.player.dispose();
            this.player = null;
        }
        

        
        console.log('视频播放器已销毁');
    }
}

/**
 * 简化的质量控制器
 * Simple Quality Controller (without network monitoring)
 */
class SimpleQualityController {
    constructor() {
        this.currentQuality = 'auto';
        this.availableQualities = ['240p', '360p', '480p', '720p', '1080p'];
        this.manualMode = false;
    }

    setAvailableQualities(qualities) {
        this.availableQualities = qualities;
        console.log('可用质量级别:', qualities);
    }

    changeQuality(quality, player, reason = 'manual') {
        if (!player || !this.availableQualities.includes(quality)) {
            console.warn('无效的质量级别:', quality);
            return;
        }

        const oldQuality = this.currentQuality;
        this.currentQuality = quality;
        this.manualMode = (reason === 'manual');

        console.log(`质量切换: ${oldQuality} -> ${quality} (${reason})`);

        // 触发质量变化事件
        if (player.trigger) {
            player.trigger('qualitychange', {
                oldQuality: oldQuality,
                newQuality: quality,
                reason: reason
            });
        }

        // 实际切换质量的逻辑
        this.applyQualityChange(quality, player);
    }

    applyQualityChange(quality, player) {
        // 简化的质量切换实现
        if (player.qualityLevels) {
            const qualityLevels = player.qualityLevels();

            // 禁用所有质量级别
            for (let i = 0; i < qualityLevels.length; i++) {
                qualityLevels[i].enabled = false;
            }

            // 启用目标质量级别
            const targetLevel = Array.from(qualityLevels).find(level =>
                level.height === this.getQualityHeight(quality)
            );

            if (targetLevel) {
                targetLevel.enabled = true;
            }
        }
    }

    getQualityHeight(quality) {
        const heightMap = {
            '2160p': 2160,
            '1080p': 1080,
            '720p': 720,
            '480p': 480,
            '360p': 360,
            '240p': 240
        };

        return heightMap[quality] || 720;
    }

    getCurrentQuality() {
        return this.currentQuality;
    }

    getQualityInfo() {
        return {
            current: this.currentQuality,
            available: this.availableQualities,
            manual: this.manualMode
        };
    }

    enableManualMode(enabled = true) {
        this.manualMode = enabled;
        console.log('手动质量控制:', enabled ? '启用' : '禁用');
    }
}
