package com.videoplayer.util;

import org.springframework.util.StringUtils;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.regex.Pattern;

/**
 * 输入验证工具类
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
public class ValidationUtils {

    // 常用正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );
    
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^1[3-9]\\d{9}$"
    );
    
    private static final Pattern URL_PATTERN = Pattern.compile(
        "^https?://[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(/.*)?$"
    );
    
    private static final Pattern VIDEO_URL_PATTERN = Pattern.compile(
        "^https?://.*\\.(mp4|avi|mov|wmv|flv|webm|mkv)$",
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern SAFE_STRING_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9\\u4e00-\\u9fa5\\s\\-_.,!?()\\[\\]]+$"
    );

    /**
     * 验证字符串是否为空或null
     */
    public static boolean isEmpty(String str) {
        return !StringUtils.hasText(str);
    }

    /**
     * 验证字符串长度
     */
    public static boolean isValidLength(String str, int minLength, int maxLength) {
        if (isEmpty(str)) {
            return minLength == 0;
        }
        int length = str.trim().length();
        return length >= minLength && length <= maxLength;
    }

    /**
     * 验证邮箱格式
     */
    public static boolean isValidEmail(String email) {
        if (isEmpty(email)) {
            return false;
        }
        return EMAIL_PATTERN.matcher(email.trim()).matches();
    }

    /**
     * 验证手机号格式
     */
    public static boolean isValidPhone(String phone) {
        if (isEmpty(phone)) {
            return false;
        }
        return PHONE_PATTERN.matcher(phone.trim()).matches();
    }

    /**
     * 验证URL格式
     */
    public static boolean isValidUrl(String url) {
        if (isEmpty(url)) {
            return false;
        }
        
        try {
            new URL(url);
            return URL_PATTERN.matcher(url).matches();
        } catch (MalformedURLException e) {
            return false;
        }
    }

    /**
     * 验证视频URL格式
     */
    public static boolean isValidVideoUrl(String url) {
        if (!isValidUrl(url)) {
            return false;
        }
        
        // 检查是否是视频文件扩展名或已知的视频服务
        return VIDEO_URL_PATTERN.matcher(url).matches() ||
               url.contains("aliyuncs.com") ||
               url.contains("oss-") ||
               url.contains("youtube.com") ||
               url.contains("vimeo.com");
    }

    /**
     * 验证安全字符串（防止XSS）
     */
    public static boolean isSafeString(String str) {
        if (isEmpty(str)) {
            return true;
        }
        
        // 检查是否包含潜在的恶意字符
        String lowerStr = str.toLowerCase();
        if (lowerStr.contains("<script") ||
            lowerStr.contains("javascript:") ||
            lowerStr.contains("onload=") ||
            lowerStr.contains("onerror=") ||
            lowerStr.contains("onclick=")) {
            return false;
        }
        
        return SAFE_STRING_PATTERN.matcher(str).matches();
    }

    /**
     * 清理字符串（移除潜在的恶意内容）
     */
    public static String sanitizeString(String str) {
        if (isEmpty(str)) {
            return str;
        }
        
        return str.trim()
                  .replaceAll("<[^>]*>", "") // 移除HTML标签
                  .replaceAll("javascript:", "") // 移除JavaScript协议
                  .replaceAll("on\\w+\\s*=", ""); // 移除事件处理器
    }

    /**
     * 验证视频标题
     */
    public static boolean isValidVideoTitle(String title) {
        return !isEmpty(title) &&
               isValidLength(title, 1, 200) &&
               isSafeString(title);
    }

    /**
     * 验证视频描述
     */
    public static boolean isValidVideoDescription(String description) {
        if (isEmpty(description)) {
            return true; // 描述可以为空
        }
        return isValidLength(description, 0, 1000) &&
               isSafeString(description);
    }

    /**
     * 验证联系人姓名
     */
    public static boolean isValidContactName(String name) {
        return !isEmpty(name) &&
               isValidLength(name, 1, 100) &&
               isSafeString(name);
    }

    /**
     * 验证微信号
     */
    public static boolean isValidWechatId(String wechatId) {
        if (isEmpty(wechatId)) {
            return true; // 微信号可以为空
        }
        
        // 微信号规则：6-20位，字母、数字、下划线、减号，字母开头
        Pattern wechatPattern = Pattern.compile("^[a-zA-Z][a-zA-Z0-9_-]{5,19}$");
        return wechatPattern.matcher(wechatId.trim()).matches();
    }

    /**
     * 验证抖音号
     */
    public static boolean isValidDouyinId(String douyinId) {
        if (isEmpty(douyinId)) {
            return true; // 抖音号可以为空
        }
        
        return isValidLength(douyinId, 1, 100) &&
               isSafeString(douyinId);
    }

    /**
     * 验证数值范围
     */
    public static boolean isValidRange(Number value, Number min, Number max) {
        if (value == null) {
            return true; // null值由其他验证处理
        }
        
        double val = value.doubleValue();
        double minVal = min != null ? min.doubleValue() : Double.MIN_VALUE;
        double maxVal = max != null ? max.doubleValue() : Double.MAX_VALUE;
        
        return val >= minVal && val <= maxVal;
    }

    /**
     * 验证视频时长（秒）
     */
    public static boolean isValidDuration(Integer duration) {
        if (duration == null) {
            return true; // 时长可以为空
        }
        return isValidRange(duration, 0, 86400); // 0-24小时
    }

    /**
     * 验证文件大小（字节）
     */
    public static boolean isValidFileSize(Long fileSize) {
        if (fileSize == null) {
            return true; // 文件大小可以为空
        }
        return isValidRange(fileSize, 0L, 10737418240L); // 0-10GB
    }

    /**
     * 验证视频格式
     */
    public static boolean isValidVideoFormat(String format) {
        if (isEmpty(format)) {
            return true; // 格式可以为空
        }
        
        String[] validFormats = {"mp4", "avi", "mov", "wmv", "flv", "webm", "mkv"};
        String lowerFormat = format.toLowerCase().trim();
        
        for (String validFormat : validFormats) {
            if (validFormat.equals(lowerFormat)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证分辨率
     */
    public static boolean isValidResolution(String resolution) {
        if (isEmpty(resolution)) {
            return true; // 分辨率可以为空
        }
        
        String[] validResolutions = {"480p", "720p", "1080p", "1440p", "2160p", "4K", "8K"};
        String trimmedResolution = resolution.trim();
        
        for (String validResolution : validResolutions) {
            if (validResolution.equalsIgnoreCase(trimmedResolution)) {
                return true;
            }
        }
        
        // 也支持 WIDTHxHEIGHT 格式
        Pattern resolutionPattern = Pattern.compile("^\\d{3,4}x\\d{3,4}$");
        return resolutionPattern.matcher(trimmedResolution).matches();
    }

    /**
     * 验证IP地址
     */
    public static boolean isValidIpAddress(String ip) {
        if (isEmpty(ip)) {
            return false;
        }
        
        // IPv4格式验证
        Pattern ipv4Pattern = Pattern.compile(
            "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
        );
        
        // IPv6格式验证（简化版）
        Pattern ipv6Pattern = Pattern.compile(
            "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$"
        );
        
        return ipv4Pattern.matcher(ip).matches() || ipv6Pattern.matcher(ip).matches();
    }

    /**
     * 验证用户代理字符串
     */
    public static boolean isValidUserAgent(String userAgent) {
        if (isEmpty(userAgent)) {
            return true; // 用户代理可以为空
        }
        
        // 检查长度和基本安全性
        return isValidLength(userAgent, 0, 500) &&
               !userAgent.contains("<script") &&
               !userAgent.contains("javascript:");
    }
}
