package com.videoplayer.controller;

import com.videoplayer.common.ApiResponse;
import com.videoplayer.service.StreamingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

/**
 * 流媒体优化控制器
 * Streaming Optimization Controller
 */
@RestController
@RequestMapping("/api/streaming")
public class StreamingController {

    private static final Logger logger = LoggerFactory.getLogger(StreamingController.class);
    
    @Autowired
    private StreamingService streamingService;
    
    /**
     * 分片视频流传输
     * 支持HTTP Range请求，实现视频分片加载
     */
    @GetMapping("/video/{videoId}")
    public ResponseEntity<Resource> streamVideo(
            @PathVariable Long videoId,
            @RequestHeader(value = "Range", required = false) String rangeHeader,
            HttpServletRequest request) {
        
        try {
            logger.info("请求视频流 - videoId: {}, Range: {}", videoId, rangeHeader);
            
            // 获取视频资源
            Resource videoResource = streamingService.getVideoResource(videoId);
            if (!videoResource.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            long contentLength = videoResource.contentLength();
            
            // 处理Range请求
            if (rangeHeader != null && rangeHeader.startsWith("bytes=")) {
                return handleRangeRequest(videoResource, rangeHeader, contentLength);
            }
            
            // 完整文件响应
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType("video/mp4"))
                    .contentLength(contentLength)
                    .header(HttpHeaders.ACCEPT_RANGES, "bytes")
                    .header(HttpHeaders.CACHE_CONTROL, "public, max-age=3600")
                    .body(videoResource);
                    
        } catch (Exception e) {
            logger.error("视频流传输失败 - videoId: {}", videoId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 处理HTTP Range请求
     */
    private ResponseEntity<Resource> handleRangeRequest(Resource resource, String rangeHeader, long contentLength) {
        try {
            // 解析Range头
            String[] ranges = rangeHeader.substring(6).split("-");
            long start = 0;
            long end = contentLength - 1;
            
            if (ranges.length > 0 && !ranges[0].isEmpty()) {
                start = Long.parseLong(ranges[0]);
            }
            
            if (ranges.length > 1 && !ranges[1].isEmpty()) {
                end = Long.parseLong(ranges[1]);
            }
            
            // 验证范围
            if (start > end || start < 0 || end >= contentLength) {
                return ResponseEntity.status(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE)
                        .header(HttpHeaders.CONTENT_RANGE, "bytes */" + contentLength)
                        .build();
            }
            
            long rangeLength = end - start + 1;
            
            // 创建部分内容资源
            Resource partialResource = streamingService.getPartialResource(resource, start, rangeLength);
            
            return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT)
                    .contentType(MediaType.parseMediaType("video/mp4"))
                    .contentLength(rangeLength)
                    .header(HttpHeaders.ACCEPT_RANGES, "bytes")
                    .header(HttpHeaders.CONTENT_RANGE, "bytes " + start + "-" + end + "/" + contentLength)
                    .header(HttpHeaders.CACHE_CONTROL, "public, max-age=3600")
                    .body(partialResource);
                    
        } catch (Exception e) {
            logger.error("处理Range请求失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取视频元信息
     */
    @GetMapping("/video/{videoId}/info")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getVideoInfo(@PathVariable Long videoId) {
        try {
            Map<String, Object> videoInfo = streamingService.getVideoInfo(videoId);
            return ResponseEntity.ok(ApiResponse.success("获取视频信息成功", videoInfo));
        } catch (Exception e) {
            logger.error("获取视频信息失败 - videoId: {}", videoId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取视频信息失败"));
        }
    }
    

    
    /**
     * 预加载视频片段
     */
    @PostMapping("/video/{videoId}/preload")
    public ResponseEntity<ApiResponse<String>> preloadVideo(
            @PathVariable Long videoId,
            @RequestParam(defaultValue = "0") long startTime,
            @RequestParam(defaultValue = "30") long duration) {
        
        try {
            logger.info("预加载视频片段 - videoId: {}, start: {}, duration: {}", videoId, startTime, duration);
            
            streamingService.preloadVideoSegment(videoId, startTime, duration);
            
            return ResponseEntity.ok(ApiResponse.success("视频片段预加载成功"));
        } catch (Exception e) {
            logger.error("预加载视频片段失败 - videoId: {}", videoId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("预加载失败"));
        }
    }
    
    /**
     * 获取视频缩略图
     */
    @GetMapping("/video/{videoId}/thumbnail")
    public ResponseEntity<Resource> getVideoThumbnail(
            @PathVariable Long videoId,
            @RequestParam(defaultValue = "0") long timeOffset) {
        
        try {
            Resource thumbnail = streamingService.generateThumbnail(videoId, timeOffset);
            
            if (!thumbnail.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok()
                    .contentType(MediaType.IMAGE_JPEG)
                    .header(HttpHeaders.CACHE_CONTROL, "public, max-age=86400") // 缓存1天
                    .body(thumbnail);
                    
        } catch (Exception e) {
            logger.error("生成视频缩略图失败 - videoId: {}", videoId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 流式传输优化响应
     */
    @GetMapping("/video/{videoId}/stream")
    public ResponseEntity<StreamingResponseBody> streamVideoOptimized(
            @PathVariable Long videoId,
            @RequestParam(defaultValue = "1024") int bufferSize,
            HttpServletResponse response) {
        
        try {
            logger.info("优化流式传输 - videoId: {}, bufferSize: {}", videoId, bufferSize);
            
            Resource videoResource = streamingService.getVideoResource(videoId);
            if (!videoResource.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            StreamingResponseBody responseBody = outputStream -> {
                try (InputStream inputStream = videoResource.getInputStream()) {
                    byte[] buffer = new byte[bufferSize];
                    int bytesRead;
                    
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                        outputStream.flush();
                        
                        // 添加小延迟以控制传输速率
                        Thread.sleep(1);
                    }
                } catch (IOException | InterruptedException e) {
                    logger.error("流式传输中断", e);
                }
            };
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType("video/mp4"))
                    .header(HttpHeaders.CACHE_CONTROL, "public, max-age=3600")
                    .body(responseBody);
                    
        } catch (Exception e) {
            logger.error("优化流式传输失败 - videoId: {}", videoId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取播放统计信息
     */
    @PostMapping("/video/{videoId}/stats")
    public ResponseEntity<ApiResponse<String>> recordPlaybackStats(
            @PathVariable Long videoId,
            @RequestBody Map<String, Object> stats) {
        
        try {
            logger.info("记录播放统计 - videoId: {}, stats: {}", videoId, stats);
            
            streamingService.recordPlaybackStats(videoId, stats);
            
            return ResponseEntity.ok(ApiResponse.success("统计信息记录成功"));
        } catch (Exception e) {
            logger.error("记录播放统计失败 - videoId: {}", videoId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("统计记录失败"));
        }
    }
    
    /**
     * 清理缓存
     */
    @DeleteMapping("/cache/{videoId}")
    public ResponseEntity<ApiResponse<String>> clearVideoCache(@PathVariable Long videoId) {
        try {
            streamingService.clearVideoCache(videoId);
            return ResponseEntity.ok(ApiResponse.success("缓存清理成功"));
        } catch (Exception e) {
            logger.error("清理缓存失败 - videoId: {}", videoId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("缓存清理失败"));
        }
    }
}
