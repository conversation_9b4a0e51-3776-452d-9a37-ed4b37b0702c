/**
 * 增强型视频播放器
 * Enhanced Video Player v2.0
 */

class EnhancedVideoPlayer {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            autoplay: false,
            controls: true,
            fluid: true,
            responsive: true,
            playbackRates: [0.5, 1, 1.25, 1.5, 2],
            ...options
        };
        
        this.player = null;
        this.analytics = {
            startTime: null,
            totalWatchTime: 0,
            maxProgress: 0,
            playCount: 0
        };
        
        this.init();
    }
    
    init() {
        if (!this.container) {
            console.error('视频容器未找到');
            return;
        }
        
        this.setupPlayer();
        this.bindEvents();
        this.initAnalytics();
    }
    
    setupPlayer() {
        // 检查是否已有Video.js实例
        if (window.videojs && this.container.querySelector('video')) {
            const videoElement = this.container.querySelector('video');
            
            this.player = videojs(videoElement, {
                ...this.options,
                plugins: {
                    hotkeys: {
                        volumeStep: 0.1,
                        seekStep: 5,
                        enableModifiersForNumbers: false
                    }
                }
            });
            
            this.setupCustomControls();
        }
    }
    
    setupCustomControls() {
        if (!this.player) return;
        
        // 添加画中画按钮
        if ('pictureInPictureEnabled' in document) {
            this.addPictureInPictureButton();
        }
        
        // 添加播放速度控制
        this.addPlaybackRateMenu();
        
        // 添加全屏按钮增强
        this.enhanceFullscreenButton();
        
        // 添加音量记忆功能
        this.setupVolumeMemory();
    }
    
    addPictureInPictureButton() {
        const pipButton = this.player.controlBar.addChild('button', {
            className: 'vjs-pip-control vjs-control vjs-button',
            innerHTML: '<span class="vjs-icon-picture-in-picture"></span>',
            title: '画中画模式'
        });
        
        pipButton.on('click', () => {
            const videoElement = this.player.el().querySelector('video');
            if (document.pictureInPictureElement) {
                document.exitPictureInPicture();
            } else {
                videoElement.requestPictureInPicture().catch(err => {
                    console.error('画中画模式启动失败:', err);
                });
            }
        });
    }
    
    addPlaybackRateMenu() {
        // Video.js 已内置播放速度控制，这里可以自定义样式
        const playbackRates = this.player.playbackRates();
        if (playbackRates && playbackRates.length > 1) {
            // 自定义播放速度菜单样式
            this.player.ready(() => {
                const rateButton = this.player.controlBar.playbackRateMenuButton;
                if (rateButton) {
                    rateButton.addClass('vjs-custom-rate-button');
                }
            });
        }
    }
    
    enhanceFullscreenButton() {
        this.player.on('fullscreenchange', () => {
            if (this.player.isFullscreen()) {
                // 全屏时的特殊处理
                document.body.classList.add('video-fullscreen');
                this.lockOrientation();
            } else {
                document.body.classList.remove('video-fullscreen');
                this.unlockOrientation();
            }
        });
    }
    
    setupVolumeMemory() {
        // 恢复上次的音量设置
        const savedVolume = localStorage.getItem('videoPlayerVolume');
        if (savedVolume !== null) {
            this.player.volume(parseFloat(savedVolume));
        }
        
        // 保存音量变化
        this.player.on('volumechange', () => {
            localStorage.setItem('videoPlayerVolume', this.player.volume());
        });
    }
    
    bindEvents() {
        if (!this.player) return;
        
        // 播放事件
        this.player.on('play', () => {
            this.onPlay();
        });
        
        // 暂停事件
        this.player.on('pause', () => {
            this.onPause();
        });
        
        // 时间更新事件
        this.player.on('timeupdate', () => {
            this.onTimeUpdate();
        });
        
        // 播放结束事件
        this.player.on('ended', () => {
            this.onEnded();
        });
        
        // 错误事件
        this.player.on('error', (error) => {
            this.onError(error);
        });
        
        // 加载完成事件
        this.player.on('loadedmetadata', () => {
            this.onLoadedMetadata();
        });
        
        // 键盘快捷键
        this.setupKeyboardShortcuts();
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // 只在视频获得焦点时响应快捷键
            if (!this.player || !this.player.el().contains(document.activeElement)) {
                return;
            }
            
            switch (e.code) {
                case 'Space':
                    e.preventDefault();
                    this.togglePlay();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.seek(-10);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.seek(10);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.adjustVolume(0.1);
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.adjustVolume(-0.1);
                    break;
                case 'KeyF':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
                case 'KeyM':
                    e.preventDefault();
                    this.toggleMute();
                    break;
            }
        });
    }
    
    initAnalytics() {
        this.analytics.startTime = Date.now();
        
        // 发送播放开始事件
        this.sendAnalytics('video_start', {
            videoId: this.getVideoId(),
            timestamp: this.analytics.startTime
        });
    }
    
    onPlay() {
        this.analytics.playCount++;
        this.analytics.playStartTime = Date.now();
        
        // 记录播放事件到后端
        this.recordPlayEvent();
        
        console.log('视频开始播放');
    }
    
    onPause() {
        if (this.analytics.playStartTime) {
            this.analytics.totalWatchTime += Date.now() - this.analytics.playStartTime;
            this.analytics.playStartTime = null;
        }
        
        console.log('视频暂停');
    }
    
    onTimeUpdate() {
        const currentTime = this.player.currentTime();
        const duration = this.player.duration();
        
        if (duration > 0) {
            const progress = (currentTime / duration) * 100;
            this.analytics.maxProgress = Math.max(this.analytics.maxProgress, progress);
            
            // 记录观看进度里程碑
            this.checkProgressMilestones(progress);
        }
    }
    
    onEnded() {
        this.analytics.maxProgress = 100;
        
        // 发送播放完成事件
        this.sendAnalytics('video_complete', {
            videoId: this.getVideoId(),
            totalWatchTime: this.analytics.totalWatchTime,
            playCount: this.analytics.playCount
        });
        
        console.log('视频播放完成');
    }
    
    onError(error) {
        console.error('视频播放错误:', error);
        
        // 发送错误事件
        this.sendAnalytics('video_error', {
            videoId: this.getVideoId(),
            error: error.message || '未知错误'
        });
        
        // 显示用户友好的错误信息
        this.showErrorMessage('视频加载失败，请刷新页面重试');
    }
    
    onLoadedMetadata() {
        console.log('视频元数据加载完成');
        
        // 恢复上次观看位置
        this.restoreWatchPosition();
    }
    
    // 工具方法
    togglePlay() {
        if (this.player.paused()) {
            this.player.play();
        } else {
            this.player.pause();
        }
    }
    
    seek(seconds) {
        const currentTime = this.player.currentTime();
        const newTime = Math.max(0, Math.min(currentTime + seconds, this.player.duration()));
        this.player.currentTime(newTime);
    }
    
    adjustVolume(delta) {
        const currentVolume = this.player.volume();
        const newVolume = Math.max(0, Math.min(1, currentVolume + delta));
        this.player.volume(newVolume);
    }
    
    toggleFullscreen() {
        if (this.player.isFullscreen()) {
            this.player.exitFullscreen();
        } else {
            this.player.requestFullscreen();
        }
    }
    
    toggleMute() {
        this.player.muted(!this.player.muted());
    }
    
    lockOrientation() {
        if (screen.orientation && screen.orientation.lock) {
            screen.orientation.lock('landscape').catch(() => {
                console.log('无法锁定屏幕方向');
            });
        }
    }
    
    unlockOrientation() {
        if (screen.orientation && screen.orientation.unlock) {
            screen.orientation.unlock();
        }
    }
    
    getVideoId() {
        // 从URL或数据属性中获取视频ID
        const urlMatch = window.location.pathname.match(/\/play\/(\d+)/);
        return urlMatch ? urlMatch[1] : null;
    }
    
    async recordPlayEvent() {
        const videoId = this.getVideoId();
        if (!videoId) return;
        
        try {
            await fetch(`/api/videos/${videoId}/play`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        } catch (error) {
            console.error('记录播放事件失败:', error);
        }
    }
    
    checkProgressMilestones(progress) {
        const milestones = [25, 50, 75, 90];
        
        milestones.forEach(milestone => {
            if (progress >= milestone && !this.analytics[`milestone_${milestone}`]) {
                this.analytics[`milestone_${milestone}`] = true;
                this.sendAnalytics('video_progress', {
                    videoId: this.getVideoId(),
                    progress: milestone
                });
            }
        });
    }
    
    restoreWatchPosition() {
        const videoId = this.getVideoId();
        if (!videoId) return;
        
        const savedPosition = localStorage.getItem(`videoPosition_${videoId}`);
        if (savedPosition && parseFloat(savedPosition) > 30) {
            const position = parseFloat(savedPosition);
            const duration = this.player.duration();
            
            if (position < duration - 30) { // 不在最后30秒内
                this.player.currentTime(position);
            }
        }
        
        // 定期保存观看位置
        setInterval(() => {
            if (!this.player.paused()) {
                localStorage.setItem(`videoPosition_${videoId}`, this.player.currentTime());
            }
        }, 5000);
    }
    
    sendAnalytics(event, data) {
        // 发送分析数据到后端或第三方分析服务
        if (window.gtag) {
            window.gtag('event', event, data);
        }
        
        console.log('Analytics:', event, data);
    }
    
    showErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'video-error-message';
        errorDiv.textContent = message;
        
        this.container.appendChild(errorDiv);
        
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }
    
    // 销毁播放器
    destroy() {
        if (this.player) {
            this.player.dispose();
            this.player = null;
        }
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    const videoContainer = document.getElementById('video-player');
    if (videoContainer) {
        window.enhancedPlayer = new EnhancedVideoPlayer('video-player');
    }
});
