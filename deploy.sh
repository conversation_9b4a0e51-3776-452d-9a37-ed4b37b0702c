#!/bin/bash

# 视频播放器部署脚本
# 作者: VideoPlayer Team
# 版本: 2.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    check_command "docker"
    check_command "docker-compose"
    log_success "依赖检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    directories=(
        "logs"
        "uploads"
        "mysql/conf.d"
        "redis"
        "nginx/ssl"
        "monitoring"
        "monitoring/grafana/dashboards"
        "monitoring/grafana/datasources"
        "static"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    log_success "目录创建完成"
}

# 创建MySQL配置文件
create_mysql_config() {
    log_info "创建MySQL配置文件..."
    
    cat > mysql/conf.d/my.cnf << 'EOF'
[mysqld]
# 基础配置
default-storage-engine=INNODB
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# 性能优化
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
innodb_flush_log_at_trx_commit=2
innodb_flush_method=O_DIRECT

# 连接配置
max_connections=200
max_connect_errors=1000
wait_timeout=28800
interactive_timeout=28800

# 查询缓存
query_cache_type=1
query_cache_size=32M

# 慢查询日志
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2

# 二进制日志
log-bin=mysql-bin
binlog_format=ROW
expire_logs_days=7

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
EOF
    
    log_success "MySQL配置文件创建完成"
}

# 创建Redis配置文件
create_redis_config() {
    log_info "创建Redis配置文件..."
    
    cat > redis/redis.conf << 'EOF'
# Redis配置文件

# 网络配置
bind 0.0.0.0
port 6379
timeout 300
keepalive 60

# 内存配置
maxmemory 128mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志配置
loglevel notice
logfile ""

# 安全配置
# requirepass your_password_here

# 客户端配置
maxclients 10000
EOF
    
    log_success "Redis配置文件创建完成"
}

# 创建Prometheus配置文件
create_prometheus_config() {
    log_info "创建Prometheus配置文件..."
    
    cat > monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'video-player'
    static_configs:
      - targets: ['video-player:5000']
    metrics_path: '/api/metrics'
    scrape_interval: 30s

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    metrics_path: '/metrics'
    scrape_interval: 30s
EOF
    
    log_success "Prometheus配置文件创建完成"
}

# 构建应用
build_application() {
    log_info "构建应用..."
    
    if [ "$1" = "--no-cache" ]; then
        docker-compose build --no-cache
    else
        docker-compose build
    fi
    
    log_success "应用构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 先启动基础服务
    docker-compose up -d mysql redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 启动应用服务
    docker-compose up -d video-player
    
    # 等待应用启动
    log_info "等待应用启动..."
    sleep 20
    
    # 启动其他服务
    docker-compose up -d nginx prometheus grafana
    
    log_success "所有服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    services=("mysql" "redis" "video-player" "nginx" "prometheus" "grafana")
    
    for service in "${services[@]}"; do
        if docker-compose ps $service | grep -q "Up"; then
            log_success "$service 服务运行正常"
        else
            log_error "$service 服务未运行"
        fi
    done
}

# 显示访问信息
show_access_info() {
    log_info "服务访问信息:"
    echo ""
    echo "🌐 应用地址:"
    echo "   主页: http://localhost"
    echo "   API文档: http://localhost/api-docs"
    echo "   Swagger UI: http://localhost/swagger-ui.html"
    echo ""
    echo "📊 监控地址:"
    echo "   Prometheus: http://localhost:9090"
    echo "   Grafana: http://localhost:3000 (admin/admin123)"
    echo ""
    echo "🗄️ 数据库:"
    echo "   MySQL: localhost:3306 (root/root123456)"
    echo "   Redis: localhost:6379"
    echo ""
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_success "服务已停止"
}

# 清理资源
cleanup() {
    log_warning "这将删除所有容器、镜像和数据卷，确定要继续吗? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "清理资源..."
        docker-compose down -v --rmi all
        docker system prune -f
        log_success "清理完成"
    else
        log_info "取消清理"
    fi
}

# 查看日志
view_logs() {
    if [ -z "$1" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$1"
    fi
}

# 备份数据
backup_data() {
    log_info "备份数据..."
    
    backup_dir="backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份数据库
    docker-compose exec mysql mysqldump -u root -proot123456 video_player > "$backup_dir/database.sql"
    
    # 备份上传文件
    if [ -d "uploads" ]; then
        cp -r uploads "$backup_dir/"
    fi
    
    # 备份日志
    if [ -d "logs" ]; then
        cp -r logs "$backup_dir/"
    fi
    
    log_success "数据备份完成: $backup_dir"
}

# 恢复数据
restore_data() {
    if [ -z "$1" ]; then
        log_error "请指定备份目录"
        exit 1
    fi
    
    backup_dir="$1"
    
    if [ ! -d "$backup_dir" ]; then
        log_error "备份目录不存在: $backup_dir"
        exit 1
    fi
    
    log_info "恢复数据从: $backup_dir"
    
    # 恢复数据库
    if [ -f "$backup_dir/database.sql" ]; then
        docker-compose exec -T mysql mysql -u root -proot123456 video_player < "$backup_dir/database.sql"
        log_success "数据库恢复完成"
    fi
    
    # 恢复上传文件
    if [ -d "$backup_dir/uploads" ]; then
        cp -r "$backup_dir/uploads" .
        log_success "上传文件恢复完成"
    fi
}

# 主函数
main() {
    case "$1" in
        "init")
            check_dependencies
            create_directories
            create_mysql_config
            create_redis_config
            create_prometheus_config
            log_success "初始化完成"
            ;;
        "build")
            build_application "$2"
            ;;
        "start")
            start_services
            check_services
            show_access_info
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            start_services
            check_services
            ;;
        "status")
            check_services
            ;;
        "logs")
            view_logs "$2"
            ;;
        "cleanup")
            cleanup
            ;;
        "backup")
            backup_data
            ;;
        "restore")
            restore_data "$2"
            ;;
        "deploy")
            check_dependencies
            create_directories
            create_mysql_config
            create_redis_config
            create_prometheus_config
            build_application
            start_services
            check_services
            show_access_info
            ;;
        *)
            echo "用法: $0 {init|build|start|stop|restart|status|logs|cleanup|backup|restore|deploy}"
            echo ""
            echo "命令说明:"
            echo "  init     - 初始化环境"
            echo "  build    - 构建应用 (可选: --no-cache)"
            echo "  start    - 启动服务"
            echo "  stop     - 停止服务"
            echo "  restart  - 重启服务"
            echo "  status   - 检查服务状态"
            echo "  logs     - 查看日志 (可选: 服务名)"
            echo "  cleanup  - 清理所有资源"
            echo "  backup   - 备份数据"
            echo "  restore  - 恢复数据 (需要备份目录)"
            echo "  deploy   - 完整部署 (init + build + start)"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
