package com.videoplayer.service;

import com.videoplayer.entity.Video;
import com.videoplayer.repository.VideoRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.lang.NonNull;

/**
 * 流媒体服务
 * Streaming Service for Video Optimization
 */
@Service
public class StreamingService {

    private static final Logger logger = LoggerFactory.getLogger(StreamingService.class);
    
    @Autowired
    private VideoRepository videoRepository;

    @Value("${app.upload.path:uploads}")
    private String uploadPath;
    
    // 视频缓存
    private final Map<Long, VideoCache> videoCache = new ConcurrentHashMap<>();
    
    // 播放统计缓存
    private final Map<Long, PlaybackStats> statsCache = new ConcurrentHashMap<>();
    
    /**
     * 获取视频资源
     */
    public Resource getVideoResource(Long videoId) throws IOException {
        Optional<Video> videoOptional = videoRepository.findById(videoId);
        
        if (videoOptional.isEmpty()) {
            throw new IOException("视频不存在: " + videoId);
        }
        
        Video video = videoOptional.get();
        String videoUrl = video.getVideoUrl();
        
        // 处理不同类型的视频URL
        if (videoUrl.startsWith("http://") || videoUrl.startsWith("https://")) {
            // 网络资源
            return new UrlResource(videoUrl);
        } else {
            // 本地文件
            Path videoPath = Paths.get(uploadPath, videoUrl);
            if (!Files.exists(videoPath)) {
                throw new IOException("视频文件不存在: " + videoPath);
            }
            return new UrlResource(videoPath.toUri());
        }
    }
    
    /**
     * 获取部分视频资源（用于Range请求）
     */
    public Resource getPartialResource(Resource fullResource, long start, long length) throws IOException {
        try (InputStream inputStream = fullResource.getInputStream()) {
            // 跳过开始位置之前的字节
            inputStream.skip(start);
            
            // 读取指定长度的数据
            byte[] buffer = new byte[(int) length];
            int totalBytesRead = 0;
            int bytesRead;

            while (totalBytesRead < length && (bytesRead = inputStream.read(buffer, totalBytesRead, (int) length - totalBytesRead)) != -1) {
                totalBytesRead += bytesRead;
            }

            // 创建部分内容的资源
            final int finalBytesRead = totalBytesRead;
            byte[] actualData = new byte[finalBytesRead];
            System.arraycopy(buffer, 0, actualData, 0, finalBytesRead);
            return new ByteArrayResource(actualData) {
                @Override
                @NonNull
                public String getDescription() {
                    return "Partial video resource [" + finalBytesRead + " bytes]";
                }
            };
        }
    }
    
    /**
     * 获取视频信息
     */
    @Cacheable(value = "videoInfo", key = "#videoId")
    public Map<String, Object> getVideoInfo(Long videoId) {
        Optional<Video> videoOptional = videoRepository.findById(videoId);
        
        if (videoOptional.isEmpty()) {
            throw new RuntimeException("视频不存在: " + videoId);
        }
        
        Video video = videoOptional.get();
        Map<String, Object> info = new HashMap<>();
        
        info.put("id", video.getId());
        info.put("title", video.getTitle());
        info.put("duration", video.getDuration());
        info.put("resolution", video.getResolution());
        info.put("fileSize", video.getFileSize());
        info.put("format", getVideoFormat(video.getVideoUrl()));
        info.put("bitrate", estimateBitrate(video));
        info.put("hasSubtitles", false); // 可以扩展字幕支持
        
        // 添加质量级别信息
        info.put("availableQualities", getAvailableQualities(video));
        
        return info;
    }
    
    /**
     * 预加载视频片段
     */
    public void preloadVideoSegment(Long videoId, long startTime, long duration) {
        try {
            logger.info("预加载视频片段 - videoId: {}, start: {}s, duration: {}s", videoId, startTime, duration);
            
            // 检查缓存中是否已有该片段
            VideoCache existingCache = videoCache.get(videoId);
            if (existingCache != null && existingCache.hasSegment(startTime, duration)) {
                logger.info("视频片段已在缓存中");
                return;
            }

            // 异步预加载
            new Thread(() -> {
                try {
                    // 这里可以实现实际的片段预加载逻辑
                    // 例如：读取特定时间段的视频数据到缓存
                    // Resource videoResource = getVideoResource(videoId);

                    VideoCache cache = videoCache.get(videoId);
                    if (cache == null) {
                        cache = new VideoCache(videoId);
                        videoCache.put(videoId, cache);
                    }

                    cache.addSegment(startTime, duration);
                    logger.info("视频片段预加载完成");

                } catch (Exception e) {
                    logger.error("预加载视频片段失败", e);
                }
            }).start();
            
        } catch (Exception e) {
            logger.error("启动预加载失败", e);
        }
    }
    
    /**
     * 生成视频缩略图
     */
    public Resource generateThumbnail(Long videoId, long timeOffset) throws IOException {
        // 这里应该实现实际的缩略图生成逻辑
        // 可以使用FFmpeg或其他视频处理库
        
        logger.info("生成视频缩略图 - videoId: {}, timeOffset: {}s", videoId, timeOffset);
        
        // 临时返回一个占位符图片
        // 实际实现中应该生成真实的缩略图
        String placeholderPath = "static/images/video-placeholder.jpg";
        Path thumbnailPath = Paths.get(placeholderPath);
        
        if (Files.exists(thumbnailPath)) {
            return new UrlResource(thumbnailPath.toUri());
        }
        
        throw new IOException("缩略图生成失败");
    }
    
    /**
     * 记录播放统计
     */
    public void recordPlaybackStats(Long videoId, Map<String, Object> stats) {
        PlaybackStats playbackStats = statsCache.computeIfAbsent(videoId, k -> new PlaybackStats(videoId));
        
        // 更新统计信息
        if (stats.containsKey("bufferEvents")) {
            playbackStats.addBufferEvents((Integer) stats.get("bufferEvents"));
        }
        
        if (stats.containsKey("qualityChanges")) {
            playbackStats.addQualityChanges((Integer) stats.get("qualityChanges"));
        }
        
        if (stats.containsKey("watchTime")) {
            playbackStats.addWatchTime((Long) stats.get("watchTime"));
        }
        
        if (stats.containsKey("bandwidth")) {
            playbackStats.updateBandwidth((Double) stats.get("bandwidth"));
        }
        
        logger.info("更新播放统计 - videoId: {}, stats: {}", videoId, playbackStats);
    }
    
    /**
     * 清理视频缓存
     */
    public void clearVideoCache(Long videoId) {
        videoCache.remove(videoId);
        statsCache.remove(videoId);
        logger.info("清理视频缓存 - videoId: {}", videoId);
    }
    
    // 辅助方法
    
    private String getVideoFormat(String videoUrl) {
        if (videoUrl.toLowerCase().endsWith(".mp4")) return "MP4";
        if (videoUrl.toLowerCase().endsWith(".webm")) return "WebM";
        if (videoUrl.toLowerCase().endsWith(".avi")) return "AVI";
        if (videoUrl.toLowerCase().endsWith(".mov")) return "MOV";
        return "Unknown";
    }
    
    private long estimateBitrate(Video video) {
        // 简单的码率估算
        if (video.getFileSize() != null && video.getDuration() != null && video.getDuration() > 0) {
            return (video.getFileSize() * 8) / video.getDuration(); // bits per second
        }
        return 0;
    }
    
    private String[] getAvailableQualities(Video video) {
        // 根据视频分辨率返回可用的质量级别
        String resolution = video.getResolution();
        
        if (resolution == null) {
            return new String[]{"360p", "480p", "720p"};
        }
        
        if (resolution.contains("2160") || resolution.contains("4K")) {
            return new String[]{"240p", "360p", "480p", "720p", "1080p", "2160p"};
        } else if (resolution.contains("1080")) {
            return new String[]{"240p", "360p", "480p", "720p", "1080p"};
        } else if (resolution.contains("720")) {
            return new String[]{"240p", "360p", "480p", "720p"};
        } else {
            return new String[]{"240p", "360p", "480p"};
        }
    }
    
    // 内部类

    /**
     * 视频缓存类
     */
    private static class VideoCache {
        private final Map<String, Boolean> segments = new ConcurrentHashMap<>();

        public VideoCache(Long videoId) {
            // videoId 用于标识缓存，但在类内部不需要存储
        }
        
        public boolean hasSegment(long startTime, long duration) {
            String key = startTime + "-" + duration;
            return segments.getOrDefault(key, false);
        }
        
        public void addSegment(long startTime, long duration) {
            String key = startTime + "-" + duration;
            segments.put(key, true);
        }
    }
    
    /**
     * 播放统计类
     */
    private static class PlaybackStats {
        private final Long videoId;
        private int bufferEvents = 0;
        private int qualityChanges = 0;
        private long totalWatchTime = 0;
        private double averageBandwidth = 0;
        
        public PlaybackStats(Long videoId) {
            this.videoId = videoId;
        }
        
        public void addBufferEvents(int events) {
            this.bufferEvents += events;
        }
        
        public void addQualityChanges(int changes) {
            this.qualityChanges += changes;
        }
        
        public void addWatchTime(long time) {
            this.totalWatchTime += time;
        }
        
        public void updateBandwidth(double bandwidth) {
            this.averageBandwidth = (this.averageBandwidth + bandwidth) / 2;
        }
        
        @Override
        public String toString() {
            return String.format("PlaybackStats{videoId=%d, bufferEvents=%d, qualityChanges=%d, watchTime=%d, bandwidth=%.2f}", 
                    videoId, bufferEvents, qualityChanges, totalWatchTime, averageBandwidth);
        }
    }
}
