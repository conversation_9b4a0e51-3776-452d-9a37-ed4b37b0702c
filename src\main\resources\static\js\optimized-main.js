/**
 * 优化后的主要JavaScript文件
 * Enhanced Main JavaScript v2.0
 */

// 全局配置
const CONFIG = {
    API_BASE_URL: '/api',
    DEBOUNCE_DELAY: 300,
    ANIMATION_DURATION: 300,
    PAGE_SIZE: 12
};

// 工具函数
const Utils = {
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 格式化时间
    formatDuration(seconds) {
        if (!seconds) return '未知';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    },

    // 格式化文件大小
    formatFileSize(bytes) {
        if (!bytes) return '未知';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
    },

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 自动关闭
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, CONFIG.ANIMATION_DURATION);
        }, 3000);
        
        // 点击关闭
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, CONFIG.ANIMATION_DURATION);
        });
    },

    // 显示加载状态
    showLoading(element) {
        const loader = document.createElement('div');
        loader.className = 'loading-overlay';
        loader.innerHTML = '<div class="loading"></div>';
        element.style.position = 'relative';
        element.appendChild(loader);
    },

    // 隐藏加载状态
    hideLoading(element) {
        const loader = element.querySelector('.loading-overlay');
        if (loader) {
            loader.remove();
        }
    }
};

// API服务
const ApiService = {
    async request(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };
        
        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || '请求失败');
            }
            
            return data;
        } catch (error) {
            console.error('API请求错误:', error);
            throw error;
        }
    },

    // 获取视频列表
    async getVideos(page = 0, size = CONFIG.PAGE_SIZE, sortBy = 'createdTime', sortDir = 'desc') {
        const params = new URLSearchParams({
            page: page.toString(),
            size: size.toString(),
            sortBy,
            sortDir
        });
        
        return this.request(`${CONFIG.API_BASE_URL}/videos?${params}`);
    },

    // 搜索视频
    async searchVideos(keyword, page = 0, size = CONFIG.PAGE_SIZE) {
        const params = new URLSearchParams({
            keyword,
            page: page.toString(),
            size: size.toString()
        });
        
        return this.request(`${CONFIG.API_BASE_URL}/videos/search?${params}`);
    },

    // 获取视频详情
    async getVideo(id) {
        return this.request(`${CONFIG.API_BASE_URL}/videos/${id}`);
    },

    // 记录播放
    async playVideo(id) {
        return this.request(`${CONFIG.API_BASE_URL}/videos/${id}/play`, {
            method: 'POST'
        });
    }
};

// 搜索功能
class SearchManager {
    constructor() {
        this.searchToggle = document.querySelector('.search-toggle');
        this.searchForm = document.querySelector('.search-form');
        this.searchInput = document.querySelector('.search-input');
        this.searchButton = document.querySelector('.search-button');
        
        this.init();
    }
    
    init() {
        if (!this.searchToggle || !this.searchForm) return;
        
        // 切换搜索表单显示
        this.searchToggle.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggleSearch();
        });
        
        // 搜索输入防抖
        if (this.searchInput) {
            this.searchInput.addEventListener('input', 
                Utils.debounce((e) => this.handleSearch(e.target.value), CONFIG.DEBOUNCE_DELAY)
            );
        }
        
        // 搜索按钮点击
        if (this.searchButton) {
            this.searchButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.performSearch();
            });
        }
        
        // 按Enter键搜索
        if (this.searchInput) {
            this.searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch();
                }
            });
        }
        
        // 点击外部关闭搜索
        document.addEventListener('click', (e) => {
            if (!this.searchForm.contains(e.target) && !this.searchToggle.contains(e.target)) {
                this.hideSearch();
            }
        });
    }
    
    toggleSearch() {
        if (this.searchForm.classList.contains('active')) {
            this.hideSearch();
        } else {
            this.showSearch();
        }
    }
    
    showSearch() {
        this.searchForm.classList.add('active');
        if (this.searchInput) {
            setTimeout(() => this.searchInput.focus(), 100);
        }
    }
    
    hideSearch() {
        this.searchForm.classList.remove('active');
    }
    
    handleSearch(keyword) {
        if (keyword.length < 2) return;
        
        // 这里可以添加实时搜索建议功能
        console.log('搜索关键词:', keyword);
    }
    
    async performSearch() {
        const keyword = this.searchInput?.value?.trim();
        if (!keyword) return;
        
        try {
            // 跳转到搜索结果页面
            window.location.href = `/videos?keyword=${encodeURIComponent(keyword)}`;
        } catch (error) {
            Utils.showNotification('搜索失败，请重试', 'error');
        }
    }
}

// 视频卡片管理
class VideoCardManager {
    constructor() {
        this.init();
    }
    
    init() {
        // 为视频卡片添加交互效果
        this.addCardInteractions();
        
        // 懒加载图片
        this.initLazyLoading();
    }
    
    addCardInteractions() {
        const videoCards = document.querySelectorAll('.video-card');
        
        videoCards.forEach(card => {
            // 添加点击效果
            card.addEventListener('click', (e) => {
                if (e.target.tagName !== 'A' && e.target.tagName !== 'BUTTON') {
                    const link = card.querySelector('a[href*="/play/"]');
                    if (link) {
                        window.location.href = link.href;
                    }
                }
            });
            
            // 添加键盘导航支持
            card.setAttribute('tabindex', '0');
            card.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    card.click();
                }
            });
        });
    }
    
    initLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => imageObserver.observe(img));
        } else {
            // 降级处理
            images.forEach(img => {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
            });
        }
    }
}

// 页面初始化
class PageManager {
    constructor() {
        this.init();
    }
    
    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
        } else {
            this.onDOMReady();
        }
    }
    
    onDOMReady() {
        // 初始化各个组件
        new SearchManager();
        new VideoCardManager();
        
        // 添加平滑滚动
        this.initSmoothScroll();
        
        // 添加返回顶部按钮
        this.initBackToTop();
        
        // 初始化工具提示
        this.initTooltips();
    }
    
    initSmoothScroll() {
        const links = document.querySelectorAll('a[href^="#"]');
        links.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                if (href === '#') return;
                
                const target = document.querySelector(href);
                if (target) {
                    e.preventDefault();
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
    
    initBackToTop() {
        const backToTop = document.createElement('button');
        backToTop.className = 'back-to-top';
        backToTop.innerHTML = '↑';
        backToTop.setAttribute('aria-label', '返回顶部');
        document.body.appendChild(backToTop);
        
        // 显示/隐藏按钮
        const toggleButton = Utils.throttle(() => {
            if (window.pageYOffset > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        }, 100);
        
        window.addEventListener('scroll', toggleButton);
        
        // 点击返回顶部
        backToTop.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
    
    initTooltips() {
        const elements = document.querySelectorAll('[data-tooltip]');
        elements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip';
                tooltip.textContent = element.dataset.tooltip;
                document.body.appendChild(tooltip);
                
                const rect = element.getBoundingClientRect();
                tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
                
                element._tooltip = tooltip;
            });
            
            element.addEventListener('mouseleave', () => {
                if (element._tooltip) {
                    element._tooltip.remove();
                    element._tooltip = null;
                }
            });
        });
    }
}

// 启动应用
new PageManager();
