package com.videoplayer.util;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.StringUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.regex.Pattern;

/**
 * 安全工具类
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
public class SecurityUtils {

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();
    private static final String ALLOWED_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    
    // SQL注入检测模式
    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
        "(?i)(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript)",
        Pattern.CASE_INSENSITIVE
    );

    /**
     * 获取客户端真实IP地址
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };

        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
                // 取第一个IP地址
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                if (ValidationUtils.isValidIpAddress(ip)) {
                    return ip;
                }
            }
        }

        return request.getRemoteAddr();
    }

    /**
     * 检测SQL注入攻击
     */
    public static boolean containsSqlInjection(String input) {
        if (!StringUtils.hasText(input)) {
            return false;
        }
        
        return SQL_INJECTION_PATTERN.matcher(input).find();
    }

    /**
     * 检测XSS攻击
     */
    public static boolean containsXss(String input) {
        if (!StringUtils.hasText(input)) {
            return false;
        }
        
        String lowerInput = input.toLowerCase();
        return lowerInput.contains("<script") ||
               lowerInput.contains("javascript:") ||
               lowerInput.contains("onload=") ||
               lowerInput.contains("onerror=") ||
               lowerInput.contains("onclick=") ||
               lowerInput.contains("onmouseover=") ||
               lowerInput.contains("<iframe") ||
               lowerInput.contains("<object") ||
               lowerInput.contains("<embed");
    }

    /**
     * 清理XSS攻击代码
     */
    public static String cleanXss(String input) {
        if (!StringUtils.hasText(input)) {
            return input;
        }
        
        return input.replaceAll("<[^>]*>", "")
                   .replaceAll("javascript:", "")
                   .replaceAll("on\\w+\\s*=", "")
                   .replaceAll("&lt;", "<")
                   .replaceAll("&gt;", ">")
                   .replaceAll("&quot;", "\"")
                   .replaceAll("&#x27;", "'")
                   .replaceAll("&#x2F;", "/");
    }

    /**
     * 生成随机字符串
     */
    public static String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(ALLOWED_CHARS.charAt(SECURE_RANDOM.nextInt(ALLOWED_CHARS.length())));
        }
        return sb.toString();
    }

    /**
     * 生成随机盐值
     */
    public static String generateSalt() {
        byte[] salt = new byte[16];
        SECURE_RANDOM.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }

    /**
     * 计算SHA-256哈希值
     */
    public static String sha256Hash(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes());
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }

    /**
     * 计算带盐值的哈希
     */
    public static String hashWithSalt(String input, String salt) {
        return sha256Hash(input + salt);
    }

    /**
     * 验证哈希值
     */
    public static boolean verifyHash(String input, String salt, String expectedHash) {
        String actualHash = hashWithSalt(input, salt);
        return actualHash.equals(expectedHash);
    }

    /**
     * 检查请求是否来自可信来源
     */
    public static boolean isTrustedReferer(HttpServletRequest request, String[] trustedDomains) {
        String referer = request.getHeader("Referer");
        if (!StringUtils.hasText(referer)) {
            return false;
        }
        
        for (String domain : trustedDomains) {
            if (referer.startsWith("https://" + domain) || referer.startsWith("http://" + domain)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查User-Agent是否可疑
     */
    public static boolean isSuspiciousUserAgent(String userAgent) {
        if (!StringUtils.hasText(userAgent)) {
            return true; // 没有User-Agent可能是爬虫
        }
        
        String lowerUserAgent = userAgent.toLowerCase();
        
        // 检查已知的恶意User-Agent模式
        String[] suspiciousPatterns = {
            "bot", "crawler", "spider", "scraper", "scanner",
            "sqlmap", "nikto", "nmap", "masscan", "zap",
            "python-requests", "curl", "wget", "httpclient"
        };
        
        for (String pattern : suspiciousPatterns) {
            if (lowerUserAgent.contains(pattern)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查请求频率是否过高
     */
    public static boolean isRateLimited(String clientId, int maxRequests, long timeWindowMs) {
        // 这里应该使用Redis或内存缓存来实现
        // 简化实现，实际项目中需要使用专门的限流组件
        return false;
    }

    /**
     * 生成CSRF令牌
     */
    public static String generateCsrfToken() {
        return generateRandomString(32);
    }

    /**
     * 验证CSRF令牌
     */
    public static boolean validateCsrfToken(String sessionToken, String requestToken) {
        return StringUtils.hasText(sessionToken) && 
               StringUtils.hasText(requestToken) && 
               sessionToken.equals(requestToken);
    }

    /**
     * 检查文件扩展名是否安全
     */
    public static boolean isSafeFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return false;
        }
        
        String lowerFilename = filename.toLowerCase();
        
        // 允许的文件扩展名
        String[] allowedExtensions = {
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", // 图片
            ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv", // 视频
            ".mp3", ".wav", ".ogg", ".aac", ".flac", // 音频
            ".pdf", ".txt", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx" // 文档
        };
        
        for (String ext : allowedExtensions) {
            if (lowerFilename.endsWith(ext)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查文件大小是否在允许范围内
     */
    public static boolean isValidFileSize(long fileSize, long maxSize) {
        return fileSize > 0 && fileSize <= maxSize;
    }

    /**
     * 生成安全的文件名
     */
    public static String generateSafeFilename(String originalFilename) {
        if (!StringUtils.hasText(originalFilename)) {
            return generateRandomString(10) + ".tmp";
        }
        
        // 移除路径分隔符和特殊字符
        String safeName = originalFilename.replaceAll("[^a-zA-Z0-9._-]", "_");
        
        // 限制长度
        if (safeName.length() > 100) {
            String extension = "";
            int dotIndex = safeName.lastIndexOf('.');
            if (dotIndex > 0) {
                extension = safeName.substring(dotIndex);
                safeName = safeName.substring(0, Math.min(95, dotIndex));
            }
            safeName = safeName + extension;
        }
        
        return safeName;
    }

    /**
     * 检查URL是否安全
     */
    public static boolean isSafeUrl(String url) {
        if (!ValidationUtils.isValidUrl(url)) {
            return false;
        }
        
        String lowerUrl = url.toLowerCase();
        
        // 检查协议
        if (!lowerUrl.startsWith("http://") && !lowerUrl.startsWith("https://")) {
            return false;
        }
        
        // 检查是否包含恶意内容
        return !lowerUrl.contains("javascript:") &&
               !lowerUrl.contains("data:") &&
               !lowerUrl.contains("vbscript:") &&
               !lowerUrl.contains("file://");
    }

    /**
     * 编码HTML特殊字符
     */
    public static String encodeHtml(String input) {
        if (!StringUtils.hasText(input)) {
            return input;
        }
        
        return input.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#x27;")
                   .replace("/", "&#x2F;");
    }

    /**
     * 解码HTML特殊字符
     */
    public static String decodeHtml(String input) {
        if (!StringUtils.hasText(input)) {
            return input;
        }
        
        return input.replace("&amp;", "&")
                   .replace("&lt;", "<")
                   .replace("&gt;", ">")
                   .replace("&quot;", "\"")
                   .replace("&#x27;", "'")
                   .replace("&#x2F;", "/");
    }

    /**
     * 检查密码强度
     */
    public static boolean isStrongPassword(String password) {
        if (!StringUtils.hasText(password) || password.length() < 8) {
            return false;
        }
        
        boolean hasUpper = password.matches(".*[A-Z].*");
        boolean hasLower = password.matches(".*[a-z].*");
        boolean hasDigit = password.matches(".*\\d.*");
        boolean hasSpecial = password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*");
        
        return hasUpper && hasLower && hasDigit && hasSpecial;
    }
}
