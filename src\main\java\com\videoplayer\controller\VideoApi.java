package com.videoplayer.controller;

import com.videoplayer.common.ApiResponse;
import com.videoplayer.entity.Video;
import com.videoplayer.exception.ResourceNotFoundException;
import com.videoplayer.service.VideoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 视频控制器
 * 提供视频相关的REST API接口
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@RestController
@RequestMapping("/api/videos")
@CrossOrigin(origins = "*")
@Tag(name = "视频管理", description = "视频相关的API接口")
public class VideoApi {

    private static final Logger logger = LoggerFactory.getLogger(VideoApi.class);

    @Autowired
    private VideoService videoService;

    /**
     * 获取所有视频列表
     */
    @Operation(summary = "获取视频列表", description = "分页获取所有激活状态的视频列表")
    @GetMapping
    public ResponseEntity<ApiResponse<List<Video>>> getAllVideos(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdTime") String sortBy,
            @Parameter(description = "排序方向，asc或desc") @RequestParam(defaultValue = "desc") String sortDir) {
        
        logger.info("获取视频列表 - page: {}, size: {}, sortBy: {}, sortDir: {}", page, size, sortBy, sortDir);
        
        Page<Video> videoPage = videoService.getAllActiveVideos(page, size, sortBy, sortDir);
        
        return ResponseEntity.ok(ApiResponse.success(
            videoPage.getContent(), 
            videoPage.getTotalElements(), 
            page, 
            size
        ));
    }

    /**
     * 根据ID获取视频详情
     */
    @Operation(summary = "获取视频详情", description = "根据视频ID获取视频的详细信息")
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Video>> getVideoById(
            @Parameter(description = "视频ID") @PathVariable Long id) {
        logger.info("获取视频详情 - id: {}", id);
        
        Optional<Video> optionalVideo = videoService.getVideoById(id);
        
        if (optionalVideo.isEmpty()) {
            throw new ResourceNotFoundException("Video", id);
        }
        
        return ResponseEntity.ok(ApiResponse.success("获取视频详情成功", optionalVideo.get()));
    }

    /**
     * 搜索视频
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<Video>>> searchVideos(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        logger.info("搜索视频 - keyword: {}, page: {}, size: {}", keyword, page, size);
        
        Page<Video> videoPage = videoService.searchVideos(keyword, page, size);
        
        return ResponseEntity.ok(ApiResponse.success(
            videoPage.getContent(), 
            videoPage.getTotalElements(), 
            page, 
            size
        ));
    }

    /**
     * 播放视频（记录播放事件）
     */
    @PostMapping("/{id}/play")
    public ResponseEntity<ApiResponse<String>> playVideo(@PathVariable Long id) {
        logger.info("播放视频 - id: {}", id);
        
        if (!videoService.existsById(id)) {
            throw new ResourceNotFoundException("Video", id);
        }
        
        return ResponseEntity.ok(ApiResponse.success("视频播放成功"));
    }

    /**
     * 获取热门视频
     */
    @GetMapping("/popular")
    public ResponseEntity<ApiResponse<List<Video>>> getPopularVideos(
            @RequestParam(defaultValue = "12") int limit) {
        
        logger.info("获取热门视频 - limit: {}", limit);
        
        List<Video> videos = videoService.getPopularVideos(limit);
        
        return ResponseEntity.ok(ApiResponse.success("获取热门视频成功", videos));
    }

    /**
     * 获取最新视频
     */
    @GetMapping("/latest")
    public ResponseEntity<ApiResponse<List<Video>>> getLatestVideos(
            @RequestParam(defaultValue = "12") int limit) {
        
        logger.info("获取最新视频 - limit: {}", limit);
        
        List<Video> videos = videoService.getLatestVideos(limit);
        
        return ResponseEntity.ok(ApiResponse.success("获取最新视频成功", videos));
    }
}
