package com.videoplayer.filter;

import com.videoplayer.util.SecurityUtils;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Enumeration;

/**
 * 安全过滤器
 * 用于检测和防护常见的Web攻击
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@Component
public class SecurityFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(SecurityFilter.class);
    
    // 可信域名列表
    private static final String[] TRUSTED_DOMAINS = {
        "localhost",
        "127.0.0.1",
        "your-domain.com"
    };

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 记录请求信息
        logRequestInfo(httpRequest);
        
        // 安全检查
        if (!performSecurityChecks(httpRequest, httpResponse)) {
            return; // 请求被拦截
        }
        
        // 添加安全响应头
        addSecurityHeaders(httpResponse);
        
        // 继续处理请求
        chain.doFilter(request, response);
    }

    /**
     * 记录请求信息
     */
    private void logRequestInfo(HttpServletRequest request) {
        String clientIp = SecurityUtils.getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String method = request.getMethod();
        String uri = request.getRequestURI();
        String queryString = request.getQueryString();
        
        logger.info("请求信息 - IP: {}, Method: {}, URI: {}, Query: {}, UserAgent: {}", 
                   clientIp, method, uri, queryString, userAgent);
    }

    /**
     * 执行安全检查
     */
    private boolean performSecurityChecks(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        // 检查User-Agent
        if (SecurityUtils.isSuspiciousUserAgent(request.getHeader("User-Agent"))) {
            logger.warn("可疑的User-Agent: {} from IP: {}", 
                       request.getHeader("User-Agent"), 
                       SecurityUtils.getClientIpAddress(request));
            // 可以选择是否拦截，这里只记录警告
        }
        
        // 检查请求参数中的SQL注入和XSS
        if (containsMaliciousContent(request)) {
            logger.error("检测到恶意请求 - IP: {}, URI: {}", 
                        SecurityUtils.getClientIpAddress(request), 
                        request.getRequestURI());
            sendSecurityError(response, "请求包含非法内容");
            return false;
        }
        
        // 检查Referer（对于敏感操作）
        if (isSensitiveOperation(request) && !isValidReferer(request)) {
            logger.warn("可疑的Referer - IP: {}, Referer: {}", 
                       SecurityUtils.getClientIpAddress(request), 
                       request.getHeader("Referer"));
            // 对于敏感操作，可以要求验证Referer
        }
        
        return true;
    }

    /**
     * 检查请求是否包含恶意内容
     */
    private boolean containsMaliciousContent(HttpServletRequest request) {
        // 检查URL参数
        String queryString = request.getQueryString();
        if (queryString != null && (SecurityUtils.containsSqlInjection(queryString) || 
                                   SecurityUtils.containsXss(queryString))) {
            return true;
        }
        
        // 检查请求参数
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String[] paramValues = request.getParameterValues(paramName);
            
            if (paramValues != null) {
                for (String paramValue : paramValues) {
                    if (SecurityUtils.containsSqlInjection(paramValue) || 
                        SecurityUtils.containsXss(paramValue)) {
                        logger.error("恶意参数 - 参数名: {}, 参数值: {}", paramName, paramValue);
                        return true;
                    }
                }
            }
        }
        
        // 检查请求头
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            
            if (headerValue != null && (SecurityUtils.containsSqlInjection(headerValue) || 
                                       SecurityUtils.containsXss(headerValue))) {
                logger.error("恶意请求头 - 头名: {}, 头值: {}", headerName, headerValue);
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查是否是敏感操作
     */
    private boolean isSensitiveOperation(HttpServletRequest request) {
        String uri = request.getRequestURI();
        String method = request.getMethod();
        
        // 定义敏感操作
        return ("POST".equals(method) || "PUT".equals(method) || "DELETE".equals(method)) &&
               (uri.contains("/admin/") || 
                uri.contains("/api/videos") && !"GET".equals(method));
    }

    /**
     * 验证Referer是否有效
     */
    private boolean isValidReferer(HttpServletRequest request) {
        return SecurityUtils.isTrustedReferer(request, TRUSTED_DOMAINS);
    }

    /**
     * 添加安全响应头
     */
    private void addSecurityHeaders(HttpServletResponse response) {
        // 防止点击劫持
        response.setHeader("X-Frame-Options", "DENY");
        
        // XSS保护
        response.setHeader("X-XSS-Protection", "1; mode=block");
        
        // 内容类型嗅探保护
        response.setHeader("X-Content-Type-Options", "nosniff");
        
        // Referrer策略
        response.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
        
        // 权限策略
        response.setHeader("Permissions-Policy", 
            "geolocation=(), microphone=(), camera=(), payment=(), usb=()");
        
        // 缓存控制（对于敏感页面）
        if (isSensitivePage(response)) {
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
        }
    }

    /**
     * 检查是否是敏感页面
     */
    private boolean isSensitivePage(HttpServletResponse response) {
        // 可以根据响应内容或其他条件判断
        return false;
    }

    /**
     * 发送安全错误响应
     */
    private void sendSecurityError(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(String.format(
            "{\"success\":false,\"message\":\"%s\",\"errorCode\":\"SECURITY_VIOLATION\"}", 
            message
        ));
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        logger.info("安全过滤器初始化完成");
    }

    @Override
    public void destroy() {
        logger.info("安全过滤器销毁");
    }
}
