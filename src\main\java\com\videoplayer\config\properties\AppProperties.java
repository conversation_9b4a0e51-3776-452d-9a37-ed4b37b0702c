package com.videoplayer.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 应用配置属性
 */
@Component
@ConfigurationProperties(prefix = "app")
public class AppProperties {

    private String name = "视频播放器";
    private String version = "2.0.0";
    private String description = "专业的网页视频播放器";
    
    private Upload upload = new Upload();
    private Security security = new Security();
    private Cache cache = new Cache();
    private Oss oss = new Oss();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Upload getUpload() {
        return upload;
    }

    public void setUpload(Upload upload) {
        this.upload = upload;
    }

    public Security getSecurity() {
        return security;
    }

    public void setSecurity(Security security) {
        this.security = security;
    }

    public Cache getCache() {
        return cache;
    }

    public void setCache(Cache cache) {
        this.cache = cache;
    }

    public Oss getOss() {
        return oss;
    }

    public void setOss(Oss oss) {
        this.oss = oss;
    }

    /**
     * 文件上传配置
     */
    public static class Upload {
        private long maxFileSize = 104857600L; // 100MB
        private List<String> allowedTypes = List.of("mp4", "avi", "mov", "wmv", "flv", "webm", "mkv");
        private String path = "uploads";

        public long getMaxFileSize() {
            return maxFileSize;
        }

        public void setMaxFileSize(long maxFileSize) {
            this.maxFileSize = maxFileSize;
        }

        public List<String> getAllowedTypes() {
            return allowedTypes;
        }

        public void setAllowedTypes(List<String> allowedTypes) {
            this.allowedTypes = allowedTypes;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }
    }

    /**
     * 安全配置
     */
    public static class Security {
        private boolean enableXssProtection = true;
        private boolean enableSqlInjectionProtection = true;
        private boolean enableCsrfProtection = false;
        private boolean enableRateLimiting = true;
        private int maxRequestsPerMinute = 100;
        private int sessionTimeoutMinutes = 30;
        private List<String> trustedDomains = List.of("localhost", "127.0.0.1");

        public boolean isEnableXssProtection() {
            return enableXssProtection;
        }

        public void setEnableXssProtection(boolean enableXssProtection) {
            this.enableXssProtection = enableXssProtection;
        }

        public boolean isEnableSqlInjectionProtection() {
            return enableSqlInjectionProtection;
        }

        public void setEnableSqlInjectionProtection(boolean enableSqlInjectionProtection) {
            this.enableSqlInjectionProtection = enableSqlInjectionProtection;
        }

        public boolean isEnableCsrfProtection() {
            return enableCsrfProtection;
        }

        public void setEnableCsrfProtection(boolean enableCsrfProtection) {
            this.enableCsrfProtection = enableCsrfProtection;
        }

        public boolean isEnableRateLimiting() {
            return enableRateLimiting;
        }

        public void setEnableRateLimiting(boolean enableRateLimiting) {
            this.enableRateLimiting = enableRateLimiting;
        }

        public int getMaxRequestsPerMinute() {
            return maxRequestsPerMinute;
        }

        public void setMaxRequestsPerMinute(int maxRequestsPerMinute) {
            this.maxRequestsPerMinute = maxRequestsPerMinute;
        }

        public int getSessionTimeoutMinutes() {
            return sessionTimeoutMinutes;
        }

        public void setSessionTimeoutMinutes(int sessionTimeoutMinutes) {
            this.sessionTimeoutMinutes = sessionTimeoutMinutes;
        }

        public List<String> getTrustedDomains() {
            return trustedDomains;
        }

        public void setTrustedDomains(List<String> trustedDomains) {
            this.trustedDomains = trustedDomains;
        }
    }

    /**
     * 缓存配置
     */
    public static class Cache {
        private boolean enabled = true;
        private int ttlMinutes = 60;
        private int maxSize = 1000;
        private String type = "redis";

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getTtlMinutes() {
            return ttlMinutes;
        }

        public void setTtlMinutes(int ttlMinutes) {
            this.ttlMinutes = ttlMinutes;
        }

        public int getMaxSize() {
            return maxSize;
        }

        public void setMaxSize(int maxSize) {
            this.maxSize = maxSize;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }

    /**
     * OSS配置
     */
    public static class Oss {
        private String endpoint = "https://oss-cn-guangzhou.aliyuncs.com";
        private String bucket = "jyqk";
        private String dir = "video-player/videos";
        private String baseUrl = "https://jyqk.oss-cn-guangzhou.aliyuncs.com";

        public String getEndpoint() {
            return endpoint;
        }

        public void setEndpoint(String endpoint) {
            this.endpoint = endpoint;
        }

        public String getBucket() {
            return bucket;
        }

        public void setBucket(String bucket) {
            this.bucket = bucket;
        }

        public String getDir() {
            return dir;
        }

        public void setDir(String dir) {
            this.dir = dir;
        }

        public String getBaseUrl() {
            return baseUrl;
        }

        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }
    }
}
