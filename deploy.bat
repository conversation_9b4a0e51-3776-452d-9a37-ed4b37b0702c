@echo off
REM 视频播放器部署脚本 (Windows版本)
REM 作者: VideoPlayer Team
REM 版本: 2.0.0

setlocal enabledelayedexpansion

REM 颜色定义 (Windows CMD不支持颜色，使用echo代替)
set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARNING=[WARNING]"
set "ERROR=[ERROR]"

REM 检查Docker和Docker Compose
:check_dependencies
echo %INFO% 检查依赖...
docker --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Docker未安装或未启动
    exit /b 1
)
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Docker Compose未安装
    exit /b 1
)
echo %SUCCESS% 依赖检查完成

REM 创建必要的目录
:create_directories
echo %INFO% 创建必要的目录...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "mysql\conf.d" mkdir mysql\conf.d
if not exist "redis" mkdir redis
if not exist "nginx\ssl" mkdir nginx\ssl
if not exist "monitoring" mkdir monitoring
if not exist "monitoring\grafana\dashboards" mkdir monitoring\grafana\dashboards
if not exist "monitoring\grafana\datasources" mkdir monitoring\grafana\datasources
if not exist "static" mkdir static
echo %SUCCESS% 目录创建完成
goto :eof

REM 创建MySQL配置文件
:create_mysql_config
echo %INFO% 创建MySQL配置文件...
(
echo [mysqld]
echo # 基础配置
echo default-storage-engine=INNODB
echo character-set-server=utf8mb4
echo collation-server=utf8mb4_unicode_ci
echo.
echo # 性能优化
echo innodb_buffer_pool_size=256M
echo innodb_log_file_size=64M
echo innodb_flush_log_at_trx_commit=2
echo innodb_flush_method=O_DIRECT
echo.
echo # 连接配置
echo max_connections=200
echo max_connect_errors=1000
echo wait_timeout=28800
echo interactive_timeout=28800
echo.
echo [mysql]
echo default-character-set=utf8mb4
echo.
echo [client]
echo default-character-set=utf8mb4
) > mysql\conf.d\my.cnf
echo %SUCCESS% MySQL配置文件创建完成
goto :eof

REM 创建Redis配置文件
:create_redis_config
echo %INFO% 创建Redis配置文件...
(
echo # Redis配置文件
echo bind 0.0.0.0
echo port 6379
echo timeout 300
echo keepalive 60
echo maxmemory 128mb
echo maxmemory-policy allkeys-lru
echo save 900 1
echo save 300 10
echo save 60 10000
echo rdbcompression yes
echo rdbchecksum yes
echo dbfilename dump.rdb
echo dir /data
echo appendonly yes
echo appendfilename "appendonly.aof"
echo appendfsync everysec
echo loglevel notice
echo logfile ""
echo maxclients 10000
) > redis\redis.conf
echo %SUCCESS% Redis配置文件创建完成
goto :eof

REM 创建Prometheus配置文件
:create_prometheus_config
echo %INFO% 创建Prometheus配置文件...
(
echo global:
echo   scrape_interval: 15s
echo   evaluation_interval: 15s
echo.
echo scrape_configs:
echo   - job_name: 'prometheus'
echo     static_configs:
echo       - targets: ['localhost:9090']
echo.
echo   - job_name: 'video-player'
echo     static_configs:
echo       - targets: ['video-player:5000']
echo     metrics_path: '/api/metrics'
echo     scrape_interval: 30s
) > monitoring\prometheus.yml
echo %SUCCESS% Prometheus配置文件创建完成
goto :eof

REM 构建应用
:build_application
echo %INFO% 构建应用...
if "%~1"=="--no-cache" (
    docker-compose build --no-cache
) else (
    docker-compose build
)
if errorlevel 1 (
    echo %ERROR% 应用构建失败
    exit /b 1
)
echo %SUCCESS% 应用构建完成
goto :eof

REM 启动服务
:start_services
echo %INFO% 启动服务...

REM 先启动基础服务
docker-compose up -d mysql redis
if errorlevel 1 (
    echo %ERROR% 基础服务启动失败
    exit /b 1
)

REM 等待数据库启动
echo %INFO% 等待数据库启动...
timeout /t 30 /nobreak >nul

REM 启动应用服务
docker-compose up -d video-player
if errorlevel 1 (
    echo %ERROR% 应用服务启动失败
    exit /b 1
)

REM 等待应用启动
echo %INFO% 等待应用启动...
timeout /t 20 /nobreak >nul

REM 启动其他服务
docker-compose up -d nginx prometheus grafana
if errorlevel 1 (
    echo %ERROR% 其他服务启动失败
    exit /b 1
)

echo %SUCCESS% 所有服务启动完成
goto :eof

REM 检查服务状态
:check_services
echo %INFO% 检查服务状态...
docker-compose ps
goto :eof

REM 显示访问信息
:show_access_info
echo %INFO% 服务访问信息:
echo.
echo 🌐 应用地址:
echo    主页: http://localhost
echo    API文档: http://localhost/api-docs
echo    Swagger UI: http://localhost/swagger-ui.html
echo.
echo 📊 监控地址:
echo    Prometheus: http://localhost:9090
echo    Grafana: http://localhost:3000 (admin/admin123)
echo.
echo 🗄️ 数据库:
echo    MySQL: localhost:3306 (root/root123456)
echo    Redis: localhost:6379
echo.
goto :eof

REM 停止服务
:stop_services
echo %INFO% 停止服务...
docker-compose down
echo %SUCCESS% 服务已停止
goto :eof

REM 查看日志
:view_logs
if "%~1"=="" (
    docker-compose logs -f
) else (
    docker-compose logs -f %1
)
goto :eof

REM 主函数
if "%1"=="init" (
    call :check_dependencies
    call :create_directories
    call :create_mysql_config
    call :create_redis_config
    call :create_prometheus_config
    echo %SUCCESS% 初始化完成
) else if "%1"=="build" (
    call :build_application %2
) else if "%1"=="start" (
    call :start_services
    call :check_services
    call :show_access_info
) else if "%1"=="stop" (
    call :stop_services
) else if "%1"=="restart" (
    call :stop_services
    call :start_services
    call :check_services
) else if "%1"=="status" (
    call :check_services
) else if "%1"=="logs" (
    call :view_logs %2
) else if "%1"=="deploy" (
    call :check_dependencies
    call :create_directories
    call :create_mysql_config
    call :create_redis_config
    call :create_prometheus_config
    call :build_application
    call :start_services
    call :check_services
    call :show_access_info
) else (
    echo 用法: %0 {init^|build^|start^|stop^|restart^|status^|logs^|deploy}
    echo.
    echo 命令说明:
    echo   init     - 初始化环境
    echo   build    - 构建应用 (可选: --no-cache)
    echo   start    - 启动服务
    echo   stop     - 停止服务
    echo   restart  - 重启服务
    echo   status   - 检查服务状态
    echo   logs     - 查看日志 (可选: 服务名)
    echo   deploy   - 完整部署 (init + build + start)
    exit /b 1
)

endlocal
