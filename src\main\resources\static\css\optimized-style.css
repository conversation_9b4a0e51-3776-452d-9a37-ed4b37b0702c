/**
 * 优化后的全局样式文件
 * Optimized Global Styles v2.0
 */

/* CSS变量定义 */
:root {
    /* 主色调 */
    --primary-color: #007bff;
    --primary-hover: #0056b3;
    --primary-light: #e3f2fd;
    
    /* 辅助色 */
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    
    /* 中性色 */
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --gray: #6c757d;
    --dark-gray: #343a40;
    --black: #000000;
    
    /* 文字颜色 */
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #868e96;
    
    /* 边框和阴影 */
    --border-color: #dee2e6;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;
    
    /* 字体 */
    --font-family-base: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-base: 1rem;
    --font-size-sm: 0.875rem;
    --font-size-lg: 1.125rem;
    --line-height-base: 1.6;
    
    /* 过渡动画 */
    --transition-base: all 0.3s ease;
    --transition-fast: all 0.15s ease;
}

/* 全局重置和基础样式 */
* {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-base);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--text-primary);
    background-color: var(--light-gray);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 改进的链接样式 */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* 按钮优化 */
.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    font-size: var(--font-size-base);
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-base);
    user-select: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-lg);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    color: var(--white);
}

/* 卡片组件优化 */
.card {
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition-base);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    padding: var(--spacing-md);
    background-color: var(--light-gray);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
}

.card-body {
    padding: var(--spacing-md);
}

.card-footer {
    padding: var(--spacing-md);
    background-color: var(--light-gray);
    border-top: 1px solid var(--border-color);
}

/* 表单优化 */
.form-control {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--text-primary);
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 导航栏优化 */
.navbar {
    background-color: var(--white);
    box-shadow: var(--box-shadow);
    padding: var(--spacing-sm) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
}

.nav-link {
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    transition: var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-radius: var(--border-radius);
}

/* 搜索组件优化 */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.search-form {
    display: none;
    align-items: center;
    gap: var(--spacing-sm);
    animation: slideIn 0.3s ease-out;
}

.search-form.active {
    display: flex;
}

.search-toggle {
    padding: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.search-toggle:hover {
    background-color: var(--light-gray);
    color: var(--primary-color);
    transform: scale(1.1);
}

/* 视频卡片优化 */
.video-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition-base);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.video-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--box-shadow-lg);
}

.video-thumbnail {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-base);
}

.video-card:hover .video-thumbnail img {
    transform: scale(1.05);
}

.video-info {
    padding: var(--spacing-md);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.video-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    flex-grow: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-top: auto;
}

/* 响应式网格 */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    :root {
        --font-size-base: 0.9rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
    }
    
    .video-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }
    
    .navbar {
        padding: var(--spacing-xs) 0;
    }
    
    .search-form {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: var(--white);
        padding: var(--spacing-sm);
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow-lg);
        min-width: 250px;
    }
}

@media (max-width: 480px) {
    .video-thumbnail {
        height: 180px;
    }

    .video-info {
        padding: var(--spacing-sm);
    }
}

/* 通知组件 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    z-index: 9999;
    min-width: 300px;
    max-width: 400px;
    animation: slideInRight 0.3s ease-out;
}

.notification-info {
    border-left: 4px solid var(--info-color);
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification-error {
    border-left: 4px solid var(--danger-color);
}

.notification-content {
    padding: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-message {
    flex-grow: 1;
    margin-right: var(--spacing-sm);
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-muted);
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    color: var(--text-primary);
}

.notification.fade-out {
    animation: slideOutRight 0.3s ease-in forwards;
}

/* 加载覆盖层 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    box-shadow: var(--box-shadow-lg);
    transition: var(--transition-base);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
}

/* 工具提示 */
.tooltip {
    position: absolute;
    background-color: var(--dark-gray);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    z-index: 9999;
    pointer-events: none;
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--dark-gray) transparent transparent transparent;
}

/* 懒加载图片 */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* 动画关键帧 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 可访问性改进 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1a1a1a;
        --light-gray: #2d2d2d;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --text-muted: #999999;
        --border-color: #404040;
        --box-shadow: 0 0.125rem 0.25rem rgba(255, 255, 255, 0.075);
        --box-shadow-lg: 0 0.5rem 1rem rgba(255, 255, 255, 0.15);
    }
}
