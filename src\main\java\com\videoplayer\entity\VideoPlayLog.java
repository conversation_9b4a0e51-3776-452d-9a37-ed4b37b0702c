package com.videoplayer.entity;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 视频播放记录实体类
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@Entity
@Table(name = "video_play_logs")
public class VideoPlayLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "video_id", nullable = false)
    private Long videoId;

    @Column(name = "user_ip", length = 45)
    private String userIp;

    @Column(name = "user_agent", columnDefinition = "TEXT")
    private String userAgent;

    @Column(name = "play_duration")
    private Integer playDuration; // 播放时长（秒）

    @Column(name = "play_progress", precision = 5, scale = 2)
    private BigDecimal playProgress; // 播放进度（百分比）

    @Enumerated(EnumType.STRING)
    @Column(name = "device_type")
    private DeviceType deviceType;

    @Column(name = "browser_type", length = 50)
    private String browserType;

    @Column(name = "referrer", length = 500)
    private String referrer;

    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;

    // 关联关系
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "video_id", insertable = false, updatable = false)
    private Video video;

    // 枚举类型
    public enum DeviceType {
        DESKTOP, MOBILE, TABLET
    }

    // 构造函数
    public VideoPlayLog() {
        this.createdTime = LocalDateTime.now();
    }

    public VideoPlayLog(Long videoId, String userIp) {
        this();
        this.videoId = videoId;
        this.userIp = userIp;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getVideoId() {
        return videoId;
    }

    public void setVideoId(Long videoId) {
        this.videoId = videoId;
    }

    public String getUserIp() {
        return userIp;
    }

    public void setUserIp(String userIp) {
        this.userIp = userIp;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public Integer getPlayDuration() {
        return playDuration;
    }

    public void setPlayDuration(Integer playDuration) {
        this.playDuration = playDuration;
    }

    public BigDecimal getPlayProgress() {
        return playProgress;
    }

    public void setPlayProgress(BigDecimal playProgress) {
        this.playProgress = playProgress;
    }

    public DeviceType getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(DeviceType deviceType) {
        this.deviceType = deviceType;
    }

    public String getBrowserType() {
        return browserType;
    }

    public void setBrowserType(String browserType) {
        this.browserType = browserType;
    }

    public String getReferrer() {
        return referrer;
    }

    public void setReferrer(String referrer) {
        this.referrer = referrer;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public Video getVideo() {
        return video;
    }

    public void setVideo(Video video) {
        this.video = video;
    }

    @Override
    public String toString() {
        return "VideoPlayLog{" +
                "id=" + id +
                ", videoId=" + videoId +
                ", userIp='" + userIp + '\'' +
                ", playDuration=" + playDuration +
                ", playProgress=" + playProgress +
                ", deviceType=" + deviceType +
                ", browserType='" + browserType + '\'' +
                ", createdTime=" + createdTime +
                '}';
    }
}
