package com.videoplayer.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 视频相关配置属性
 */
@Component
@ConfigurationProperties(prefix = "video")
public class VideoProperties {

    private Oss oss = new Oss();
    private Security security = new Security();

    public Oss getOss() {
        return oss;
    }

    public void setOss(Oss oss) {
        this.oss = oss;
    }

    public Security getSecurity() {
        return security;
    }

    public void setSecurity(Security security) {
        this.security = security;
    }

    /**
     * OSS配置
     */
    public static class Oss {
        private String endpoint;
        private String accessKeyId;
        private String accessKeySecret;
        private String bucketName;

        public String getEndpoint() {
            return endpoint;
        }

        public void setEndpoint(String endpoint) {
            this.endpoint = endpoint;
        }

        public String getAccessKeyId() {
            return accessKeyId;
        }

        public void setAccessKeyId(String accessKeyId) {
            this.accessKeyId = accessKeyId;
        }

        public String getAccessKeySecret() {
            return accessKeySecret;
        }

        public void setAccessKeySecret(String accessKeySecret) {
            this.accessKeySecret = accessKeySecret;
        }

        public String getBucketName() {
            return bucketName;
        }

        public void setBucketName(String bucketName) {
            this.bucketName = bucketName;
        }
    }

    /**
     * 安全配置
     */
    public static class Security {
        private boolean enableXssProtection = true;
        private boolean enableSqlInjectionProtection = true;
        private boolean enableCsrfProtection = false;
        private boolean enableRateLimiting = true;
        private int maxRequestsPerMinute = 100;
        private int sessionTimeoutMinutes = 30;

        public boolean isEnableXssProtection() {
            return enableXssProtection;
        }

        public void setEnableXssProtection(boolean enableXssProtection) {
            this.enableXssProtection = enableXssProtection;
        }

        public boolean isEnableSqlInjectionProtection() {
            return enableSqlInjectionProtection;
        }

        public void setEnableSqlInjectionProtection(boolean enableSqlInjectionProtection) {
            this.enableSqlInjectionProtection = enableSqlInjectionProtection;
        }

        public boolean isEnableCsrfProtection() {
            return enableCsrfProtection;
        }

        public void setEnableCsrfProtection(boolean enableCsrfProtection) {
            this.enableCsrfProtection = enableCsrfProtection;
        }

        public boolean isEnableRateLimiting() {
            return enableRateLimiting;
        }

        public void setEnableRateLimiting(boolean enableRateLimiting) {
            this.enableRateLimiting = enableRateLimiting;
        }

        public int getMaxRequestsPerMinute() {
            return maxRequestsPerMinute;
        }

        public void setMaxRequestsPerMinute(int maxRequestsPerMinute) {
            this.maxRequestsPerMinute = maxRequestsPerMinute;
        }

        public int getSessionTimeoutMinutes() {
            return sessionTimeoutMinutes;
        }

        public void setSessionTimeoutMinutes(int sessionTimeoutMinutes) {
            this.sessionTimeoutMinutes = sessionTimeoutMinutes;
        }
    }
}
