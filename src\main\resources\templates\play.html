<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle}">视频播放</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">

    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/play-style.css" rel="stylesheet">

    <!-- 性能优化样式 -->
    <style>
        .video-performance-overlay {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            display: none;
        }



        .video-buffer-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 1001;
            display: none;
        }


    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 搜索框 -->
                <div class="search-container">
                    <form class="d-flex search-form" action="/search" method="get">
                        <input class="form-control me-2 search-input-mobile" type="search" name="keyword" placeholder="搜索...">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm ms-1" type="button" onclick="toggleSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </form>
                    <button class="btn btn-outline-light btn-sm search-toggle" onclick="toggleSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-fluid px-3 py-4">
        <div class="row justify-content-center">
            <!-- 视频播放区域 -->
            <div class="col-12 col-md-8 col-lg-6">
                <!-- 视频播放器容器 -->
                <div class="video-player-container mb-4">
                    <div class="video-wrapper">
                        <!-- 响应式视频播放器 -->
                        <video
                            id="video-player"
                            controls
                            preload="metadata"
                            playsinline
                            th:data-video-url="${video.videoUrl}"
                            th:data-video-id="${video.id}"
                            th:poster="${video.thumbnailUrl}">
                            <source th:src="${video.videoUrl}" type="video/mp4">
                            您的浏览器不支持HTML5视频播放。
                        </video>
                        <!-- 加载状态 -->
                        <div class="video-loading" id="video-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>

                        <!-- 缓冲指示器 -->
                        <div class="video-buffer-indicator" id="buffer-indicator">
                            <div class="spinner-border text-light" role="status"></div>
                            <div class="mt-2">缓冲中...</div>
                        </div>





                        <!-- 性能统计覆盖层 -->
                        <div class="video-performance-overlay" id="performance-overlay">
                            <div><strong>性能统计</strong></div>
                            <div>加载时间: <span id="load-time">-</span>ms</div>
                            <div>缓冲事件: <span id="buffer-count">0</span></div>
                            <div>质量切换: <span id="quality-changes">0</span></div>
                        </div>
                    </div>
                </div>

                <!-- 视频信息 -->
                <div class="video-info bg-white rounded-3 shadow-sm p-4 mb-4">
                    <h1 class="video-title h4 mb-3" th:text="${video.title}">视频标题</h1>
                    <div class="video-stats">
                        <time class="video-date text-muted" th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd')}">2024-01-01</time>
                    </div>
                    <!-- 视频描述 -->
                    <div class="video-description mt-3" th:if="${video.description}">
                        <p class="text-muted mb-0" th:text="${video.description}">视频描述</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <!-- <h5><i class="fas fa-play-circle me-2"></i>佳茵轻康</h5> -->
                    <p class="mb-0">轻康自然，享瘦生活。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">联系我们&nbsp;&nbsp;&nbsp;</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 导航栏修复JS -->
    <script src="/js/navbar-fix.js"></script>
    <!-- 自定义JS -->
    <script src="/js/main.js"></script>


    <script th:inline="javascript">
        // 初始化响应式HTML5视频播放器
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');
            const videoUrl = videoElement.dataset.videoUrl;
            const videoId = videoElement.dataset.videoId;
            const loadingElement = document.getElementById('video-loading');

            // 设置视频源
            if (videoUrl) {
                videoElement.src = videoUrl;
            }

            // 生成视频首帧缩略图
            function generateThumbnail() {
                if (!videoElement.poster) {
                    videoElement.addEventListener('loadeddata', function() {
                        try {
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');

                            // 设置画布尺寸
                            canvas.width = videoElement.videoWidth;
                            canvas.height = videoElement.videoHeight;

                            // 绘制视频首帧
                            ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                            // 转换为base64图片
                            const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.8);
                            videoElement.poster = thumbnailDataUrl;

                            console.log('视频首帧缩略图生成成功');
                        } catch (error) {
                            console.warn('无法生成视频缩略图:', error);
                        }
                    });
                }
            }

            // 隐藏加载状态
            function hideLoading() {
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }
            }

            // 显示加载状态
            function showLoading() {
                if (loadingElement) {
                    loadingElement.style.display = 'flex';
                }
            }

            // 初始化加载状态
            showLoading();

            // 生成缩略图
            generateThumbnail();

            // 视频加载开始
            videoElement.addEventListener('loadstart', function() {
                console.log('开始加载视频');
                showLoading();
            });

            // 视频元数据加载完成
            videoElement.addEventListener('loadedmetadata', function() {
                console.log('视频元数据加载完成');
                hideLoading();
            });

            // 视频数据加载完成
            videoElement.addEventListener('loadeddata', function() {
                console.log('视频数据加载完成');
                hideLoading();
            });

            // 视频可以播放
            videoElement.addEventListener('canplay', function() {
                console.log('视频可以播放');
                hideLoading();
            });

            // 错误处理
            videoElement.addEventListener('error', function(e) {
                console.error('视频播放出错:', e);
                hideLoading();

                let errorMessage = '视频加载失败，请检查网络连接。';

                if (videoElement.error) {
                    console.error('错误代码:', videoElement.error.code);
                    console.error('错误信息:', videoElement.error.message);

                    switch (videoElement.error.code) {
                        case 1:
                            errorMessage = '视频加载被中止。';
                            break;
                        case 2:
                            errorMessage = '网络错误，无法加载视频。';
                            break;
                        case 3:
                            errorMessage = '视频解码失败或格式不支持。';
                            break;
                        case 4:
                            errorMessage = '视频不存在或无法访问。';
                            break;
                    }
                }

                // 显示错误信息
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger mt-3';
                errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${errorMessage}`;
                videoElement.parentNode.appendChild(errorDiv);
            });

            // 播放开始事件
            videoElement.addEventListener('play', function() {
                console.log('视频开始播放，ID:', videoId);
            });

            // 视频暂停事件
            videoElement.addEventListener('pause', function() {
                console.log('视频已暂停，ID:', videoId);
            });

            // 视频结束事件
            videoElement.addEventListener('ended', function() {
                console.log('视频播放结束，ID:', videoId);
            });

            console.log('响应式HTML5视频播放器已准备就绪');
        });














    </script>

    <!-- Video.js -->
    <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>

    <!-- 优化的视频播放器 -->
    <script src="/js/optimized-video-player.js"></script>

    <!-- 初始化优化播放器 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 使用优化的播放器替换原有播放器
            const videoElement = document.getElementById('video-player');
            const videoUrl = videoElement.dataset.videoUrl;
            const videoId = videoElement.dataset.videoId;

            if (videoUrl && videoId) {
                // 初始化优化播放器
                const optimizedPlayer = new OptimizedVideoPlayer('video-player', {
                    preload: 'metadata',
                    autoplay: false,
                    fluid: true,
                    responsive: true,
                    playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 2],
                    preloadNext: true
                });

                // 绑定性能监控UI更新
                setInterval(() => {
                    updatePerformanceUI(optimizedPlayer);
                }, 1000);

                // 添加键盘快捷键提示
                showKeyboardShortcuts();
            }
        });

        function updatePerformanceUI(player) {
            if (!player || typeof player.getPerformanceMetrics !== 'function') {
                return;
            }

            try {
                const metrics = player.getPerformanceMetrics();

                if (!metrics) {
                    return;
                }

                // 更新性能统计
                const loadTime = document.getElementById('load-time');
                const bufferCount = document.getElementById('buffer-count');
                const qualityChanges = document.getElementById('quality-changes');

                if (loadTime && metrics.firstFrameTime) {
                    loadTime.textContent = metrics.firstFrameTime.toFixed(0);
                }

                if (bufferCount && metrics.bufferEvents) {
                    bufferCount.textContent = metrics.bufferEvents.length;
                }

                if (qualityChanges && metrics.qualityChanges) {
                    qualityChanges.textContent = metrics.qualityChanges.length;
                }
            } catch (error) {
                console.warn('更新性能UI失败:', error);
            }
        }

        function showKeyboardShortcuts() {
            // 显示键盘快捷键提示（可选）
            console.log('键盘快捷键:');
            console.log('空格键: 播放/暂停');
            console.log('←/→: 快退/快进 10秒');
            console.log('↑/↓: 音量增减');
            console.log('F: 全屏切换');
            console.log('M: 静音切换');
        }

        // 显示/隐藏性能统计
        function togglePerformanceOverlay() {
            const overlay = document.getElementById('performance-overlay');
            if (overlay) {
                overlay.style.display = overlay.style.display === 'none' ? 'block' : 'none';
            }
        }

        // 双击显示性能统计
        document.addEventListener('dblclick', function(e) {
            if (e.target.closest('.video-wrapper')) {
                togglePerformanceOverlay();
            }
        });
    </script>
</body>
</html>

