<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API文档 - 视频播放器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .api-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            background-color: #f8f9fa;
        }
        .method-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-weight: bold;
        }
        .method-get { background-color: #28a745; color: white; }
        .method-post { background-color: #007bff; color: white; }
        .method-put { background-color: #ffc107; color: black; }
        .method-delete { background-color: #dc3545; color: white; }
        .code-block {
            background-color: #f1f3f4;
            padding: 1rem;
            border-radius: 0.25rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }
        .response-example {
            background-color: #e8f5e8;
            border-left: 4px solid #28a745;
        }
        .request-example {
            background-color: #e3f2fd;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-play-circle me-2"></i>视频播放器
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">首页</a>
                <a class="nav-link" href="/swagger-ui.html">Swagger UI</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5>API目录</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><a href="#video-api" class="text-decoration-none">视频管理API</a></li>
                            <li><a href="#contact-api" class="text-decoration-none">联系信息API</a></li>
                            <li><a href="#metrics-api" class="text-decoration-none">性能监控API</a></li>
                            <li><a href="#common-responses" class="text-decoration-none">通用响应格式</a></li>
                            <li><a href="#error-codes" class="text-decoration-none">错误代码</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-9">
                <h1>API文档</h1>
                <p class="lead">视频播放器系统API接口文档</p>

                <!-- 视频管理API -->
                <section id="video-api" class="api-section">
                    <h2><i class="fas fa-video me-2"></i>视频管理API</h2>
                    
                    <div class="mb-4">
                        <h4>
                            <span class="method-badge method-get">GET</span>
                            /api/videos
                        </h4>
                        <p><strong>描述：</strong>获取视频列表（分页）</p>
                        <p><strong>参数：</strong></p>
                        <ul>
                            <li><code>page</code> (int, 可选): 页码，从0开始，默认0</li>
                            <li><code>size</code> (int, 可选): 每页大小，默认10</li>
                            <li><code>sortBy</code> (string, 可选): 排序字段，默认createdTime</li>
                            <li><code>sortDir</code> (string, 可选): 排序方向，asc或desc，默认desc</li>
                        </ul>
                        <div class="code-block response-example">
                            <strong>响应示例：</strong><br>
                            <pre>{
  "success": true,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "title": "示例视频",
      "description": "这是一个示例视频",
      "videoUrl": "https://example.com/video.mp4",
      "thumbnailUrl": "https://example.com/thumb.jpg",
      "duration": 120,
      "createdTime": "2024-01-01T10:00:00"
    }
  ],
  "total": 100,
  "page": 0,
  "size": 10,
  "timestamp": "2024-01-01T10:00:00"
}</pre>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h4>
                            <span class="method-badge method-get">GET</span>
                            /api/videos/{id}
                        </h4>
                        <p><strong>描述：</strong>根据ID获取视频详情</p>
                        <p><strong>路径参数：</strong></p>
                        <ul>
                            <li><code>id</code> (long): 视频ID</li>
                        </ul>
                    </div>

                    <div class="mb-4">
                        <h4>
                            <span class="method-badge method-post">POST</span>
                            /api/videos
                        </h4>
                        <p><strong>描述：</strong>添加新视频</p>
                        <div class="code-block request-example">
                            <strong>请求体示例：</strong><br>
                            <pre>{
  "title": "新视频标题",
  "description": "视频描述",
  "videoUrl": "https://example.com/video.mp4",
  "thumbnailUrl": "https://example.com/thumb.jpg",
  "duration": 120
}</pre>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h4>
                            <span class="method-badge method-get">GET</span>
                            /api/videos/search
                        </h4>
                        <p><strong>描述：</strong>搜索视频</p>
                        <p><strong>参数：</strong></p>
                        <ul>
                            <li><code>keyword</code> (string, 必需): 搜索关键词</li>
                            <li><code>page</code> (int, 可选): 页码，默认0</li>
                            <li><code>size</code> (int, 可选): 每页大小，默认10</li>
                        </ul>
                    </div>
                </section>

                <!-- 通用响应格式 -->
                <section id="common-responses" class="api-section">
                    <h2><i class="fas fa-code me-2"></i>通用响应格式</h2>
                    <p>所有API接口都使用统一的响应格式：</p>
                    <div class="code-block">
                        <pre>{
  "success": boolean,      // 请求是否成功
  "message": string,       // 响应消息
  "data": object,          // 响应数据（可选）
  "errorCode": string,     // 错误代码（失败时）
  "timestamp": datetime,   // 响应时间戳
  "total": number,         // 总记录数（分页时）
  "page": number,          // 当前页码（分页时）
  "size": number           // 每页大小（分页时）
}</pre>
                    </div>
                </section>

                <!-- 错误代码 -->
                <section id="error-codes" class="api-section">
                    <h2><i class="fas fa-exclamation-triangle me-2"></i>错误代码</h2>
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>错误代码</th>
                                <th>HTTP状态码</th>
                                <th>描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>RESOURCE_NOT_FOUND</td>
                                <td>404</td>
                                <td>请求的资源不存在</td>
                            </tr>
                            <tr>
                                <td>VALIDATION_ERROR</td>
                                <td>400</td>
                                <td>请求参数验证失败</td>
                            </tr>
                            <tr>
                                <td>SECURITY_VIOLATION</td>
                                <td>400</td>
                                <td>安全违规，请求被拦截</td>
                            </tr>
                            <tr>
                                <td>INTERNAL_ERROR</td>
                                <td>500</td>
                                <td>系统内部错误</td>
                            </tr>
                        </tbody>
                    </table>
                </section>

                <!-- 使用说明 -->
                <section class="api-section">
                    <h2><i class="fas fa-info-circle me-2"></i>使用说明</h2>
                    <div class="alert alert-info">
                        <h5>认证</h5>
                        <p>当前版本的API不需要认证，所有接口都可以直接访问。</p>
                    </div>
                    <div class="alert alert-warning">
                        <h5>限流</h5>
                        <p>为了保护系统稳定性，API接口实施了限流策略，每分钟最多100次请求。</p>
                    </div>
                    <div class="alert alert-success">
                        <h5>CORS</h5>
                        <p>API支持跨域请求，允许从任何域名访问。</p>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 视频播放器. All rights reserved.</p>
            <p class="mb-0">
                <a href="/swagger-ui.html" class="text-decoration-none">Swagger UI</a> |
                <a href="/v3/api-docs" class="text-decoration-none">OpenAPI JSON</a>
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
