package com.videoplayer.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI配置类
 * 
 * <AUTHOR>
 * @version 2.0.0
 */
@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("视频播放器 API")
                        .version("2.0.0")
                        .description("专业的网页视频播放器API文档")
                        .contact(new Contact()
                                .name("VideoPlayer Team")
                                .email("<EMAIL>")
                                .url("https://videoplayer.com"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:5000")
                                .description("开发环境"),
                        new Server()
                                .url("https://api.videoplayer.com")
                                .description("生产环境")
                ));
    }
}
